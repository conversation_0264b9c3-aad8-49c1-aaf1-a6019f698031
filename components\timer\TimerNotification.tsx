import React, { useEffect, useRef } from 'react';
import * as Notifications from 'expo-notifications';

import { Audio } from 'expo-av';
import * as Haptics from 'expo-haptics';

type TimerNotificationProps = {
  onTimerComplete?: (notification: Notifications.Notification) => void;
};

// Configure how notifications are handled when the app is in the foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

const TimerNotification: React.FC<TimerNotificationProps> = ({
  onTimerComplete,
}) => {
  const notificationListener = useRef<Notifications.EventSubscription | null>(
    null
  );
  const responseListener = useRef<Notifications.EventSubscription | null>(null);
  const soundRef = useRef<Audio.Sound | null>(null);

  useEffect(() => {
    // Request permissions on mount
    const requestPermissions = async () => {
      const { status } = await Notifications.requestPermissionsAsync();
      if (status !== 'granted') {
        console.warn('Notification permissions not granted');
      }
    };

    requestPermissions();

    // Load sound
    const loadSound = async () => {
      try {
        const { sound } = await Audio.Sound.createAsync(
          require('../../../assets/sounds/timer-complete.mp3')
        );
        soundRef.current = sound;
      } catch (error) {
        console.error('Error loading sound', error);
      }
    };

    loadSound();

    // This listener is called when a notification is received while the app is in the foreground
    notificationListener.current =
      Notifications.addNotificationReceivedListener((notification) => {
        if (notification.request.content.data.timerId) {
          // Play sound and haptic feedback when timer completes
          if (soundRef.current) {
            soundRef.current.replayAsync().catch(console.error);
          }
          Haptics.notificationAsync(
            Haptics.NotificationFeedbackType.Success
          ).catch(console.error);

          if (onTimerComplete) {
            onTimerComplete(notification);
          }
        }
      });

    // This listener is called when a user taps on a notification
    responseListener.current =
      Notifications.addNotificationResponseReceivedListener((response) => {
        // Handle notification tap if needed
        console.log('Notification tapped:', response);
      });

    // Clean up on unmount
    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(
          notificationListener.current
        );
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
      if (soundRef.current) {
        soundRef.current.unloadAsync().catch(console.error);
      }
    };
  }, [onTimerComplete]);

  return null;
};

export default TimerNotification;
