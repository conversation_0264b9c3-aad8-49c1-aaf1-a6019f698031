import * as React from "react";
import { Pressable, type PressableProps } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Text } from "@/components/ui/text";
import { cn } from "@/lib/utils";

interface ChipProps extends PressableProps {
  label: string;
  icon?: React.ComponentProps<typeof MaterialCommunityIcons>["name"];
  selected?: boolean;
}

export function Chip({
  label,
  icon,
  selected = false,
  className,
  ...props
}: ChipProps) {
  return (
    <Pressable
      className={cn(
        "rounded-full border px-3 py-1 flex-row items-center mr-2 mb-2",
        selected
          ? "bg-primary bg-opacity-20 border-primary"
          : "bg-background border-border",
        className
      )}
      {...props}
    >
      {icon && (
        <MaterialCommunityIcons
          name={icon}
          size={16}
          className={cn(selected ? "text-primary" : "text-foreground", "mr-2")}
        />
      )}
      <Text className={selected ? "text-primary" : "text-foreground"}>
        {label}
      </Text>
    </Pressable>
  );
}
