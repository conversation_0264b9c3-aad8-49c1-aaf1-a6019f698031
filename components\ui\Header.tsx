import React from 'react';
import { View, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { ChevronLeft } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { theme } from '@/constants/theme';
import { Text } from './text';

type HeaderProps = {
  title: string;
  showBackButton?: boolean;
  rightElement?: React.ReactNode;
};

export const Header = ({
  title,
  showBackButton = false,
  rightElement,
}: HeaderProps) => {
  //
  const router = useRouter();

  return (
    <View className="border-b border-border" style={[styles.header]}>
      <View style={styles.leftContainer}>
        {showBackButton && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ChevronLeft size={20} color={theme.colors.gray[800]} />
          </TouchableOpacity>
        )}
      </View>

      <Text style={styles.title} className="capitalize">
        {title}
      </Text>

      <View style={styles.rightContainer}>{rightElement}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.m,
    paddingHorizontal: theme.spacing.l,
    borderBottomWidth: 0.5,
  },
  leftContainer: {
    width: 32,
  },
  backButton: {
    padding: theme.spacing.xs,
  },
  title: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
  },
  rightContainer: {
    width: 32,
    alignItems: 'flex-end',
  },
});
