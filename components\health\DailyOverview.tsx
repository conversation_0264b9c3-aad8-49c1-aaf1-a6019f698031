import React from 'react';
import { View } from 'react-native';
import {
  Droplet,
  Apple,
  Fish,
  Wheat,
  Bone,
  Shield,
  Zap,
  Heart,
  Sparkles,
  Salad,
  Sun,
} from 'lucide-react-native';
import Animated, { FadeInDown } from 'react-native-reanimated';
import { Card } from '../ui/card';
import { Text } from '../ui/text';
import { Progress } from '../ui/progress';
import { theme } from '@/constants/theme';
import { Doc } from '@/convex/_generated/dataModel';
import { healthPlansData } from '@/data/healthPlansData';

const AnimatedCard = Animated.createAnimatedComponent(Card);

type HealthPlan = Doc<'healthPlan'>;

interface DailyOverviewProps {
  mealsSummary:
    | {
        totalCalories: number;
        totalProtein: number;
        totalCarbs: number;
        totalFat: number;
        totalFiber?: number;
        totalSugar?: number;
        totalSodium?: number;
        totalCalcium?: number;
        totalIron?: number;
        totalVitaminC?: number;
        totalVitaminD?: number;
        totalWater?: number;
        mealsByType: {
          breakfast: number;
          lunch: number;
          dinner: number;
          snacks: number;
        };
      }
    | undefined;
  activePlan?: HealthPlan;
}

interface StatCardProps {
  icon: React.ReactElement;
  label: string;
  value: number;
  target: number;
  unit: string;
  iconColor: string;
}

const StatCard: React.FC<StatCardProps> = ({
  icon,
  label,
  value,
  target,
  unit,
  iconColor,
}) => (
  <View className="flex-1 border rounded-md shadow border-border items-center p-2">
    {/* Icon at top */}
    <View className="items-center justify-center mb-2">
      {React.cloneElement(icon, {
        // @ts-ignore
        size: 24,
        color: iconColor,
      })}
    </View>

    {/* Label */}
    <Text className="font-medium text-xs text-gray-600 mb-1 text-center">
      {label}
    </Text>

    {/* Value/Target at bottom */}
    <Text className="font-bold text-base text-muted-foreground text-center">
      {Math.round(value * 10) / 10}
    </Text>
    <Text className="font-normal text-xs text-gray-500 text-center">
      / {target} {unit}
    </Text>
  </View>
);

export function DailyOverview({
  mealsSummary,
  activePlan,
}: DailyOverviewProps) {
  // Define default values for when mealsSummary is undefined
  const defaultMealsSummary = {
    totalCalories: 0,
    totalProtein: 0,
    totalCarbs: 0,
    totalFat: 0,
    totalFiber: 0,
    totalSugar: 0,
    totalSodium: 0,
    totalCalcium: 0,
    totalIron: 0,
    totalVitaminC: 0,
    totalVitaminD: 0,
    totalWater: 0,
    mealsByType: {
      breakfast: 0,
      lunch: 0,
      dinner: 0,
      snacks: 0,
    },
  };

  // Use provided mealsSummary or fall back to defaults
  const summary = mealsSummary || defaultMealsSummary;

  // Get nutrition targets from active plan or use defaults
  const getNutritionTargets = () => {
    if (activePlan?.dailyNutrition) {
      return {
        calories: activePlan.dailyNutrition.calories,
        protein: activePlan.dailyNutrition.protein,
        carbs: activePlan.dailyNutrition.carbs,
        fat: activePlan.dailyNutrition.fat,
        fiber: activePlan.dailyNutrition.fiber,
        sugar: activePlan.dailyNutrition.sugar,
        sodium: activePlan.dailyNutrition.sodium,
        water: activePlan.dailyNutrition.water / 1000, // Convert ml to liters
        calcium: activePlan.dailyNutrition.calcium,
        iron: activePlan.dailyNutrition.iron,
        vitaminC: activePlan.dailyNutrition.vitaminC,
        vitaminD: activePlan.dailyNutrition.vitaminD,
      };
    }

    // If no plan nutrition data, try to get from healthPlansData
    if (activePlan?.type) {
      const planData = healthPlansData.find(
        (plan) => plan.type === activePlan.type,
      );
      if (planData) {
        return {
          calories: planData.dailyNutrition.calories,
          protein: planData.dailyNutrition.protein,
          carbs: planData.dailyNutrition.carbs,
          fat: planData.dailyNutrition.fat,
          fiber: planData.dailyNutrition.fiber,
          sugar: planData.dailyNutrition.sugar,
          sodium: planData.dailyNutrition.sodium,
          water: planData.dailyNutrition.water / 1000, // Convert ml to liters
          calcium: planData.dailyNutrition.calcium,
          iron: planData.dailyNutrition.iron,
          vitaminC: planData.dailyNutrition.vitaminC,
          vitaminD: planData.dailyNutrition.vitaminD,
        };
      }
    }

    // Default fallback values
    return {
      calories: 2000,
      protein: 150,
      carbs: 250,
      fat: 65,
      fiber: 35,
      sugar: 50,
      sodium: 1800,
      water: 2.5,
      calcium: 1000,
      iron: 15,
      vitaminC: 90,
      vitaminD: 600,
    };
  };

  const targets = getNutritionTargets();

  const calorieProgress = (summary.totalCalories / targets.calories) * 100;

  // Define all nutrition items with their data
  const nutritionItems = [
    {
      icon: <Fish />,
      label: 'Protein',
      value: summary.totalProtein || 0,
      target: targets.protein,
      unit: 'g',
      iconColor: 'skyblue',
    },
    {
      icon: <Wheat />,
      label: 'Carbs',
      value: summary.totalCarbs || 0,
      target: targets.carbs,
      unit: 'g',
      iconColor: theme.colors.warning,
    },
    {
      icon: <Apple />,
      label: 'Fat',
      value: summary.totalFat || 0,
      target: targets.fat,
      unit: 'g',
      iconColor: theme.colors.error,
    },
    {
      icon: <Salad />,
      label: 'Fiber',
      value: summary.totalFiber || 0,
      target: targets.fiber,
      unit: 'g',
      iconColor: '#4CAF50',
    },
    {
      icon: <Sparkles />,
      label: 'Sugar',
      value: summary.totalSugar || 0,
      target: targets.sugar,
      unit: 'g',
      iconColor: '#FF9800',
    },
    {
      icon: <Heart />,
      label: 'Sodium',
      value: summary.totalSodium || 0,
      target: targets.sodium,
      unit: 'mg',
      iconColor: '#F44336',
    },
    {
      icon: <Droplet />,
      label: 'Water',
      value: (summary.totalWater || 0) / 1000, // Convert ml to liters
      target: targets.water,
      unit: 'L',
      iconColor: '#2196F3',
    },
    {
      icon: <Bone />,
      label: 'Calcium',
      value: summary.totalCalcium || 0,
      target: targets.calcium,
      unit: 'mg',
      iconColor: '#9C27B0',
    },
    {
      icon: <Shield />,
      label: 'Iron',
      value: summary.totalIron || 0,
      target: targets.iron,
      unit: 'mg',
      iconColor: '#795548',
    },
    {
      icon: <Zap />,
      label: 'Vitamin C',
      value: summary.totalVitaminC || 0,
      target: targets.vitaminC,
      unit: 'mg',
      iconColor: '#CDDC39',
    },
    {
      icon: <Sun />,
      label: 'Vitamin D',
      value: summary.totalVitaminD || 0,
      target: targets.vitaminD,
      unit: 'IU',
      iconColor: '#FFC107',
    },
  ];


  
  return (
    <AnimatedCard
      className="mx-6 mt-8 shadow-lg border-0 bg-white"
      entering={FadeInDown.delay(200)}
    >
      <View className="flex-row justify-between items-center mb-4">
        <Text className="font-bold text-lg text-gray-800">
          🌞 Daily Overview
        </Text>
        <Text className="font-medium text-lg text-gray-700">
          {Math.round(summary.totalCalories)} / {targets.calories} kcal
        </Text>
      </View>

      <View className="h-4 w-full bg-gray-200 rounded-full mb-6">
        <Progress
          value={calorieProgress}
          className="h-4"
          indicatorClassName="bg-primary"
        />
      </View>

      {/* Nutrition Grid - 3 columns */}
      <View className="flex-row gap-3 flex-wrap">
        {nutritionItems.map((item, index) => (
          <View key={index} style={{ width: '31%' }}>
            <StatCard
              icon={item.icon}
              label={item.label}
              value={item.value}
              target={item.target}
              unit={item.unit}
              iconColor={item.iconColor}
            />
          </View>
        ))}
      </View>
    </AnimatedCard>
  );
}
