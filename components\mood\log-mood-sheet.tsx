import React, { use<PERSON>allback, useMemo, useRef, useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Smile,
  Frown,
  Meh,
  Zap,
  Cloud,
  Heart,
  Moon,
  Leaf,
} from 'lucide-react-native';
import { cn } from '@/lib/utils';
import { LinearGradient } from 'expo-linear-gradient';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import moment from 'moment';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { CheckCircle2 } from 'lucide-react-native';
import { AlertCircle } from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';

interface LogMoodSheetProps {
  isOpen: boolean;
  onClose: () => void;
}

const moodOptions = [
  'happy',
  'sad',
  'anxious',
  'angry',
  'neutral',
  'excited',
  'tired',
  'stressed',
  'calm',
  'other',
];

const getMoodIcon = (
  mood: string,
  size: number = 20,
  color: string = '#fff',
) => {
  switch (mood) {
    case 'happy':
      return <Smile size={size} color={color} />;
    case 'sad':
      return <Frown size={size} color={color} />;
    case 'anxious':
      return <Cloud size={size} color={color} />;
    case 'angry':
      return <Zap size={size} color={color} />;
    case 'excited':
      return <Heart size={size} color={color} />;
    case 'tired':
      return <Moon size={size} color={color} />;
    case 'stressed':
      return <Cloud size={size} color={color} />;
    case 'calm':
      return <Leaf size={size} color={color} />;
    case 'neutral':
    case 'other':
    default:
      return <Meh size={size} color={color} />;
  }
};

const moodFeedback = [
  { min: 1, max: 2, text: 'Very Low', color: '#64748b' },
  { min: 3, max: 4, text: 'Low', color: '#60a5fa' },
  { min: 5, max: 6, text: 'Moderate', color: '#facc15' },
  { min: 7, max: 8, text: 'High', color: '#34d399' },
  { min: 9, max: 10, text: 'Very High', color: '#f472b6' },
];

const getMoodFeedback = (value: number) => {
  return (
    moodFeedback.find((f) => value >= f.min && value <= f.max) ||
    moodFeedback[2]
  );
};

const moodColors: Record<string, string> = {
  happy: '#facc15', // yellow
  sad: '#60a5fa', // blue
  anxious: '#a78bfa', // purple
  angry: '#f87171', // red
  neutral: '#a3a3a3', // gray
  excited: '#f472b6', // pink
  tired: '#cbd5e1', // light blue/gray
  stressed: '#fbbf24', // orange
  calm: '#34d399', // green
  other: '#818cf8', // indigo
};

const moodGradients: Record<string, string[]> = {
  happy: ['#fde047', '#facc15'],
  sad: ['#93c5fd', '#60a5fa'],
  anxious: ['#c4b5fd', '#a78bfa'],
  angry: ['#fca5a5', '#f87171'],
  neutral: ['#d4d4d4', '#a3a3a3'],
  excited: ['#f9a8d4', '#f472b6'],
  tired: ['#e2e8f0', '#cbd5e1'],
  stressed: ['#fcd34d', '#fbbf24'],
  calm: ['#6ee7b7', '#34d399'],
  other: ['#a5b4fc', '#818cf8'],
};

const LogMoodSheet = ({ isOpen, onClose }: LogMoodSheetProps) => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const snapPoints = useMemo(() => ['50%', '75%'], []);

  const createMoodEntry = useMutation(api.mood.createMoodEntry);

  const [selectedMood, setSelectedMood] = useState<string | null>(null);
  const [intensity, setIntensity] = useState<number>(5);
  const [note, setNote] = useState<string>('');
  const [isLogging, setIsLogging] = useState(false);
  const [logSuccess, setLogSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSheetChanges = useCallback(
    (index: number) => {
      if (index === -1) {
        onClose();
        resetForm();
      }
    },
    [onClose],
  );

  const resetForm = () => {
    setSelectedMood(null);
    setIntensity(5);
    setNote('');
    setIsLogging(false);
    setLogSuccess(false);
    setError(null);
  };

  const handleLogMood = async () => {
    if (!selectedMood) {
      setError('Please select a mood.');
      return;
    }

    setIsLogging(true);
    setError(null);
    try {
      await createMoodEntry({
        date: moment().format('YYYY-MM-DD'),
        mood: selectedMood as any, // Cast to any as v.union types are strict
      });
      setLogSuccess(true);
      setTimeout(() => {
        bottomSheetRef.current?.close();
      }, 1500); // Close after a short delay to show success
    } catch (err) {
      console.error('Failed to log mood:', err);
      setError('Failed to log mood. Please try again.');
    } finally {
      setIsLogging(false);
    }
  };

  // Reanimated shared values for mood selection
  const moodScale = useRef(moodOptions.map(() => useSharedValue(1))).current;
  const moodRotation = useRef(moodOptions.map(() => useSharedValue(0))).current;

  // Reanimated shared value for slider
  const sliderValue = useSharedValue(intensity);

  // Animate mood selection
  const handleMoodPress = (mood: string, idx: number) => {
    setSelectedMood(mood);
    moodScale.forEach((s, i) => {
      s.value = withSpring(i === idx ? 1.2 : 1, {
        damping: 10,
        stiffness: 120,
      });
    });
    moodRotation.forEach((r, i) => {
      if (i === idx) {
        r.value = withSequence(
          withTiming(-6, { duration: 40 }),
          withRepeat(withTiming(6, { duration: 80 }), 4, true),
          withTiming(0, { duration: 40 }),
        );
      } else {
        r.value = withTiming(0, { duration: 100 });
      }
    });
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={isOpen ? 1 : -1} // Start at index 1 (50%) when open, -1 when closed
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      enablePanDownToClose={true}
      style={{
        borderTopWidth: 1,
        borderTopColor: '#c000',
      }}
      backgroundStyle={{ backgroundColor: '#fcfcfc' }}
      handleIndicatorStyle={{ backgroundColor: 'black' }}
    >
      <BottomSheetScrollView
        contentContainerStyle={{
          paddingBottom: 20,
          borderTopWidth: 1,
          borderTopColor: '#c000',
        }}
      >
        <View className="px-4 flex-row items-center justify-between">
          <Text className="text-gray-900 text-3xl font-extrabold tracking-tight drop-shadow-lg">
            Log Your Mood
          </Text>
        </View>

        {/* Mood Selection with Animation */}
        <View className="mb-6">
          <Label className="px-4 text-muted-foreground text-lg mb-3 font-semibold">
            How are you feeling?
          </Label>
          <View className="flex-row flex-wrap justify-center gap-x-6 gap-y-4 px-4 py-2 mb-2">
            {moodOptions.map((mood, idx) => {
              const animatedStyle = useAnimatedStyle(() => ({
                transform: [
                  { scale: moodScale[idx].value },
                  { rotate: `${moodRotation[idx].value}deg` },
                ],
                shadowOpacity: selectedMood === mood ? 0.3 : 0.08,
                shadowRadius: selectedMood === mood ? 15 : 4,
                shadowColor: selectedMood === mood ? moodColors[mood] : '#000',
              }));
              return (
                <Animated.View key={mood} style={[animatedStyle]}>
                  <View className="items-center gap-y-2">
                    <TouchableOpacity
                      onPress={() => handleMoodPress(mood, idx)}
                      className={cn(
                        'items-center justify-center rounded-full',
                        selectedMood === mood
                          ? 'border-2 border-white/80'
                          : 'border border-transparent',
                      )}
                      style={{
                        width: 64,
                        height: 64,
                        elevation: selectedMood === mood ? 8 : 2,
                      }}
                      activeOpacity={0.85}
                    >
                      <LinearGradient
                        colors={moodGradients[mood]}
                        className="w-full h-full items-center justify-center rounded-full"
                      >
                        {getMoodIcon(mood, 32, '#fff')}
                      </LinearGradient>
                    </TouchableOpacity>
                    <Text
                      className={cn(
                        'text-xs capitalize font-semibold text-center',
                        selectedMood === mood
                          ? 'text-gray-900'
                          : 'text-gray-500',
                      )}
                    >
                      {mood}
                    </Text>
                  </View>
                </Animated.View>
              );
            })}
          </View>
        </View>

        {/* Error & Success Alerts */}
        {error && (
          <Alert icon={AlertCircle} className="mb-4 bg-red-100 border-red-300">
            <AlertTitle className="text-red-700">Error</AlertTitle>
            <AlertDescription className="text-red-500">
              {error}
            </AlertDescription>
          </Alert>
        )}
        {logSuccess && (
          <Alert
            icon={CheckCircle2}
            className="mb-4 bg-green-100 border-green-300"
          >
            <AlertTitle className="text-green-700">Mood Logged!</AlertTitle>
            <AlertDescription className="text-green-500">
              Your feelings have been recorded.
            </AlertDescription>
          </Alert>
        )}

        {/* Log Button */}
        {!logSuccess && (
          <View
            className={cn(
              'mx-4 rounded-lg',
              isLogging || !selectedMood ? 'opacity-60' : 'opacity-100',
            )}
          >
            <LinearGradient
              colors={
                selectedMood
                  ? [moodColors[selectedMood], '#4f46e5']
                  : ['#a3a3a3', '#6b7280']
              }
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              className="rounded-lg"
            >
              <Button
                onPress={handleLogMood}
                disabled={isLogging || !selectedMood}
                variant="ghost"
                size={'lg'}
                className="bg-transparent w-full"
              >
                <Text className="text-white font-bold">
                  {isLogging ? 'Logging...' : 'Log Mood'}
                </Text>
              </Button>
            </LinearGradient>
          </View>
        )}
      </BottomSheetScrollView>
    </BottomSheet>
  );
};

export default LogMoodSheet;
