import { mutation, query } from './_generated/server';
import { v } from 'convex/values';
import { Id } from './_generated/dataModel';

// Mutations
export const addSleepEntry = mutation({
  args: {
    date: v.string(),
    startTime: v.string(),
    endTime: v.string(),
    durationHours: v.number(),
    qualityRating: v.number(),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const sleepId = await ctx.db.insert('sleep', { userId, ...args });
    return sleepId;
  },
});

export const updateSleepEntry = mutation({
  args: {
    _id: v.id('sleep'),
    date: v.string(),
    startTime: v.string(),
    endTime: v.string(),
    durationHours: v.number(),
    qualityRating: v.number(),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const existingSleep = await ctx.db.get(args._id);
    if (!existingSleep || existingSleep.userId !== userId) {
      throw new Error('Unauthorized or sleep entry not found');
    }

    const { _id, ...rest } = args;
    await ctx.db.patch(_id, rest);
  },
});

export const deleteSleepEntry = mutation({
  args: { _id: v.id('sleep') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const existingSleep = await ctx.db.get(args._id);
    if (!existingSleep || existingSleep.userId !== userId) {
      throw new Error('Unauthorized or sleep entry not found');
    }

    await ctx.db.delete(args._id);
  },
});

// Queries
export const getSleepByDate = query({
  args: { date: v.string() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    return await ctx.db
      .query('sleep')
      .filter((q) => q.eq(q.field('userId'), userId))
      .filter((q) => q.eq(q.field('date'), args.date))
      .first();
  },
});

export const getSleepRange = query({
  args: { startDate: v.string(), endDate: v.string() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const startIso = new Date(args.startDate).toISOString();
    const endIsoDate = new Date(args.endDate);
    endIsoDate.setDate(endIsoDate.getDate() + 1); // Go to the start of the next day
    const endIso = endIsoDate.toISOString();

    return await ctx.db
      .query('sleep')
      .filter((q) => q.eq(q.field('userId'), userId))
      .filter((q) =>
        q.and(
          q.gte(q.field('date'), startIso),
          q.lt(q.field('date'), endIso), // Use less than for exclusive end
        ),
      )
      .collect();
  },
});

export const getAverageSleepQuality = query({
  args: { startDate: v.string(), endDate: v.string() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const startIso = new Date(args.startDate).toISOString();
    const endIsoDate = new Date(args.endDate);
    endIsoDate.setDate(endIsoDate.getDate() + 1); // Go to the start of the next day
    const endIso = endIsoDate.toISOString();

    const sleepEntries = await ctx.db
      .query('sleep')
      .filter((q) => q.eq(q.field('userId'), userId))
      .filter((q) =>
        q.and(
          q.gte(q.field('date'), startIso),
          q.lt(q.field('date'), endIso),
        ),
      )
      .collect();

    if (sleepEntries.length === 0) {
      return 0;
    }

    const totalQuality = sleepEntries.reduce(
      (sum, entry) => sum + entry.qualityRating,
      0,
    );
    return totalQuality / sleepEntries.length;
  },
});

export const getAverageSleepDuration = query({
  args: { startDate: v.string(), endDate: v.string() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const startIso = new Date(args.startDate).toISOString();
    const endIsoDate = new Date(args.endDate);
    endIsoDate.setDate(endIsoDate.getDate() + 1); // Go to the start of the next day
    const endIso = endIsoDate.toISOString();

    const sleepEntries = await ctx.db
      .query('sleep')
      .filter((q) => q.eq(q.field('userId'), userId))
      .filter((q) =>
        q.and(
          q.gte(q.field('date'), startIso),
          q.lt(q.field('date'), endIso),
        ),
      )
      .collect();

    if (sleepEntries.length === 0) {
      return 0;
    }

    const totalDuration = sleepEntries.reduce(
      (sum, entry) => sum + entry.durationHours,
      0,
    );
    return totalDuration / sleepEntries.length;
  },
});

export const getLastSleepEntry = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    return await ctx.db
      .query('sleep')
      .filter((q) => q.eq(q.field('userId'), userId))
      .order('desc') // Order by _creationTime or a specific date field if available and indexed
      .first();
  },
});

export const getLast7DaysSleepSummary = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize to start of day

    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 6); // Go back 6 days to include today, making it 7 days total

    const nextDay = new Date(today);
    nextDay.setDate(today.getDate() + 1); // Start of the next day for exclusive upper bound

    const sleepEntries = await ctx.db
      .query('sleep')
      .filter((q) => q.eq(q.field('userId'), userId))
      .filter((q) =>
        q.and(
          q.gte(q.field('date'), sevenDaysAgo.toISOString()), // Compare with full ISO string
          q.lt(q.field('date'), nextDay.toISOString()), // Compare with full ISO string (exclusive)
        ),
      )
      .collect();

    const dailySleep: { [key: string]: number } = {};

    // Initialize dailySleep for the last 7 days with 0
    for (let i = 0; i < 7; i++) {
      const date = new Date(sevenDaysAgo);
      date.setDate(sevenDaysAgo.getDate() + i);
      dailySleep[date.toISOString().split('T')[0]] = 0; // Key by YYYY-MM-DD
    }

    sleepEntries.forEach((entry) => {
      const entryDateKey = entry.date.split('T')[0]; // Extract YYYY-MM-DD from stored ISO string
      if (dailySleep[entryDateKey] !== undefined) {
        dailySleep[entryDateKey] += entry.durationHours;
      }
    });

    const summary = Object.keys(dailySleep)
      .sort()
      .map((dateString) => {
        const date = new Date(dateString);
        const dayLabel = date.toLocaleDateString('en-US', {
          weekday: 'short',
          day: 'numeric',
        });
        return {
          value: parseFloat(dailySleep[dateString].toFixed(2)),
          label: dayLabel,
          color: '#8884d8', // Example color, can be dynamic
        };
      });

    return summary;
  },
});
