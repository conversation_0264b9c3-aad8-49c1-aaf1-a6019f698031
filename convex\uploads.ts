import { mutation, action } from './_generated/server';
import { v } from 'convex/values';

// Mutation to generate a URL for uploading a file
export const generateUploadUrl = mutation({
  handler: async (ctx) => {
    return await ctx.storage.generateUploadUrl();
  },
});

// Action to get the URL of an uploaded file
export const getImageUrl = action({
  args: {
    storageId: v.id('_storage'),
  },
  handler: async (ctx, args) => {
    return await ctx.storage.getUrl(args.storageId);
  },
});

// Action to upload an image and return its URL
export const uploadImage = action({
  args: {
    file: v.any(), // This will be the Blob or File object from the client
  },
  handler: async (ctx, args) => {
    // This action is not directly used for the initial upload,
    // but rather to get the URL after the client has uploaded to the signed URL.
    // The client-side function will handle the actual PUT request.
    // This action could be used if we were to handle the upload entirely on the server,
    // but for direct client-to-storage upload, generateUploadUrl is used first.
    throw new Error("This action is not meant for direct file upload. Use generateUploadUrl and then PUT to the returned URL.");
  },
});
