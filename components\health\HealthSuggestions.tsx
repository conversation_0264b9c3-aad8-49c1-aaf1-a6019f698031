import React from "react";
import { View, TouchableOpacity } from "react-native";
import { FlashList } from "@shopify/flash-list";
import { Text } from "../ui/text";
import { Doc } from "@/convex/_generated/dataModel";
import { planAdvices } from "@/data/planAdvices";

type HealthPlan = Doc<"healthPlan">;

interface HealthSuggestionsProps {
  activePlan?: HealthPlan;
}

type Suggestion = {
  text: string;
  message: string;
  icon: string;
  color: string;
  type: "advice" | "action";
};

// Default suggestions when no plan is active
const defaultSuggestions: Suggestion[] = [
  {
    text: "Create a health plan to get personalized advice",
    message:
      "Setting up a health plan helps you get targeted recommendations based on your specific goals and needs.",
    icon: "📋",
    color: "#6366f1",
    type: "advice",
  },
  {
    text: "Track your meals daily for better insights",
    message:
      "Recording what you eat helps identify patterns and makes it easier to make healthier choices.",
    icon: "📝",
    color: "#34d399",
    type: "advice",
  },
  {
    text: "Stay hydrated throughout the day",
    message:
      "Drinking enough water supports your metabolism, energy levels, and overall health.",
    icon: "💧",
    color: "#60a5fa",
    type: "advice",
  },
];

export function HealthSuggestions({ activePlan }: HealthSuggestionsProps) {
  // Get plan-specific suggestions or use defaults
  const getSuggestions = (): Suggestion[] => {
    if (!activePlan) {
      return defaultSuggestions;
    }

    const planAdviceData = planAdvices[activePlan.type];
    if (!planAdviceData) {
      return defaultSuggestions;
    }

    // Combine advices and recommended actions
    const allSuggestions: Suggestion[] = [
      ...planAdviceData.advices.map((advice) => ({
        ...advice,
        type: "advice" as const,
      })),
      ...planAdviceData.recommendedActions.map((action) => ({
        ...action,
        type: "action" as const,
      })),
    ];

    return allSuggestions;
  };

  const suggestions = getSuggestions();

  const renderSuggestion = ({ item }: { item: Suggestion }) => (
    <View className="bg-white rounded-xl p-4 mb-3 mx-4 shadow-sm border border-gray-100">
      <View className="flex-row items-start">
        <View
          className="w-12 h-12 rounded-full items-center justify-center mr-4 mt-1"
          style={{ backgroundColor: `${item.color}15` }}
        >
          <Text className="text-2xl">{item.icon}</Text>
        </View>
        <View className="flex-1">
          <Text className="text-gray-900 font-semibold text-base mb-2">
            {item.text}
          </Text>
          <Text className="text-gray-600 text-sm mb-3 leading-5">
            {item.message}
          </Text>
          <View className="flex-row">
            <View
              className="px-2 py-1 rounded-md"
              style={{ backgroundColor: `${item.color}20` }}
            >
              <Text
                className="text-xs font-medium capitalize"
                style={{ color: item.color }}
              >
                {item.type === "advice"
                  ? "Health Advice"
                  : "Recommended Action"}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <View className="flex-1 mt-4 bg-gray-50">
      <View className="px-4 py-3 bg-white border-b border-gray-100">
        <Text className="text-gray-900 font-bold text-lg">
          {activePlan ? "Plan Suggestions" : "Health Tips"}
        </Text>
        <Text className="text-gray-600 text-sm mt-1">
          {activePlan
            ? `Personalized advice for your ${activePlan.type} health plan`
            : "General health tips to get you started"}
        </Text>
      </View>

      <FlashList
        data={suggestions}
        renderItem={renderSuggestion}
        estimatedItemSize={160}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingTop: 8, paddingBottom: 16 }}
      />
    </View>
  );
}
