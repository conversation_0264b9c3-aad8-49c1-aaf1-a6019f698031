import React from 'react';
import { View, ScrollView } from 'react-native';
import { Doc } from '@/convex/_generated/dataModel';
import { healthPlansData } from '@/data/healthPlansData';
import { Text } from '../ui/text';
import { Badge } from '../ui/badge';

type HealthPlan = Doc<'healthPlan'>;

interface PlanNutritionOverviewProps {
  activePlan: HealthPlan;
}

interface NutritionCardProps {
  label: string;
  value: string;
  unit: string;
  icon: string;
}

export function PlanNutritionOverview({
  activePlan,
}: PlanNutritionOverviewProps) {
  const planData = healthPlansData.find(
    (plan) => plan.type === activePlan.type,
  );

  if (!planData) {
    return null;
  }

  const { keyBenefits, recommendedDuration } = planData;

  return (
    <ScrollView className="flex-1 mt-2" showsVerticalScrollIndicator={false}>
      <View className="px-4 py-6">
        {/* Key Benefits */}
        <View className="mb-6">
          <Text className="font-semibold text-gray-900 mb-4">Key Benefits</Text>
          <View className="flex-row flex-wrap gap-2">
            {keyBenefits.map((benefit, index) => (
              <Badge key={index}>
                <Text className="text-sm font-medium">{benefit}</Text>
              </Badge>
            ))}
          </View>
        </View>

        {/* Recommended Duration */}
        <View className="bg-gray-50 rounded-xl p-4 border border-gray-100">
          <View className="flex-row items-center">
            <Text className="text-3xl mr-2">⏱️</Text>
            <View className="flex-1">
              <Text className="text-sm font-medium text-gray-700">
                Recommended Duration
              </Text>
              <Text className="text-base font-semibold text-gray-900">
                {recommendedDuration}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}
