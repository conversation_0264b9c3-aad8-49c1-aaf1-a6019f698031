import * as React from "react";
import { ActivityIndicator, View } from "react-native";
import { cn } from "@/lib/utils";

interface SpinnerProps {
  size?: "small" | "large" | number;
  className?: string;
}

function Spinner({ size = "small", className, ...props }: SpinnerProps) {
  return (
    <View className={cn("flex-row justify-center items-center", className)}>
      <ActivityIndicator
        size={size}
        color="var(--primary-foreground)"
        {...props}
      />
    </View>
  );
}

export { Spinner };
export type { SpinnerProps };
