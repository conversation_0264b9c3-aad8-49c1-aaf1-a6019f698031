import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card } from '../ui/card';
import { Text } from '../ui/text';
import { Progress } from '../ui/progress';

export function WeeklyProgressCard() {
  const days = ['Sat', 'Sun', 'Mon', 'Tue', 'Wed'];
  const values = [55, 28, 63, 57, 100];

  return (
    <Card style={styles.card}>
      <Text style={styles.title}>Weekly Progress</Text>

      <View style={styles.progressBars}>
        {days.map((day, index) => (
          <View key={day} style={styles.dayProgress}>
            <Progress
              value={values[index]}
              style={[styles.verticalProgress, { height: `${values[index]}%` }]}
            />
            <Text style={styles.dayLabel}>{day}</Text>
          </View>
        ))}
      </View>

      <View style={styles.stats}>
        <StatItem label="Current Weight" value="90.22 kg" trend="-1.2" />
        <StatItem label="Current Calories" value="1220 kcal" trend="-8.2" />
      </View>
    </Card>
  );
}

function StatItem({
  label,
  value,
  trend,
}: {
  label: string;
  value: string;
  trend: string;
}) {
  const isPositive = !trend.startsWith('-');

  return (
    <View style={styles.statItem}>
      <Text style={styles.statLabel}>{label}</Text>
      <View style={styles.statValue}>
        <Text style={styles.value}>{value}</Text>
        <Text
          style={[styles.trend, { color: isPositive ? '#22c55e' : '#ef4444' }]}
        >
          {trend}%
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    padding: 16,
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 24,
  },
  progressBars: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: 200,
    marginBottom: 24,
  },
  dayProgress: {
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  verticalProgress: {
    width: 8,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
  },
  dayLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  stats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
  },
  statItem: {
    flex: 1,
    backgroundColor: '#f8f8f8',
    padding: 12,
    borderRadius: 12,
  },
  statLabel: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 4,
  },
  statValue: {
    flexDirection: 'row',
    alignItems: 'baseline',
    gap: 8,
  },
  value: {
    fontSize: 16,
    fontWeight: '600',
  },
  trend: {
    fontSize: 12,
  },
});
