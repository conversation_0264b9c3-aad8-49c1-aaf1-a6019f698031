import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useTimer } from '../../contexts/TimerContext';
import { Ionicons } from '@expo/vector-icons';

type TimerControlsProps = {
  timerId: string;
  title: string;
  duration: number; // in seconds
  isActive: boolean;
  remainingTime: number;
  onEdit?: () => void;
};

const TimerControls: React.FC<TimerControlsProps> = ({
  timerId,
  title,
  duration,
  isActive,
  remainingTime,
  onEdit,
}) => {
  const { toggleTimer, removeTimer, resetTimer } = useTimer();
  const [displayTime, setDisplayTime] = useState('');

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  };

  // Update display time every second
  useEffect(() => {
    setDisplayTime(formatTime(remainingTime));

    let interval: NodeJS.Timeout | null = null;

    if (isActive && remainingTime > 0) {
      interval = setInterval(() => {
        setDisplayTime((prev) => {
          const [mins, secs] = prev.split(':').map(Number);
          let totalSeconds = mins * 60 + secs - 1;

          if (totalSeconds <= 0) {
            clearInterval(interval as NodeJS.Timeout);
            return '00:00';
          }

          return formatTime(totalSeconds);
        });
      }, 1000) as unknown as NodeJS.Timeout;
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, remainingTime]);

  const handleToggle = () => {
    toggleTimer(timerId);
  };

  const handleReset = () => {
    resetTimer(timerId);
  };

  const handleRemove = () => {
    removeTimer(timerId);
  };

  // Calculate progress percentage
  const progress = ((duration - remainingTime) / duration) * 100;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title} numberOfLines={1} ellipsizeMode="tail">
          {title}
        </Text>
        <View style={styles.actions}>
          {onEdit && (
            <TouchableOpacity onPress={onEdit} style={styles.actionButton}>
              <Ionicons name="pencil" size={20} color="#666" />
            </TouchableOpacity>
          )}
          <TouchableOpacity onPress={handleRemove} style={styles.actionButton}>
            <Ionicons name="trash-outline" size={20} color="#ff4444" />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.timeContainer}>
        <Text style={styles.timeText}>{displayTime}</Text>
      </View>

      <View style={styles.progressBar}>
        <View
          style={[
            styles.progressFill,
            {
              width: `${Math.min(100, Math.max(0, progress))}%`,
              backgroundColor: progress >= 100 ? '#4CAF50' : '#2196F3',
            },
          ]}
        />
      </View>

      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.controlButton, styles.resetButton]}
          onPress={handleReset}
        >
          <Ionicons name="refresh" size={20} color="#fff" />
          <Text style={styles.controlButtonText}>Reset</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.controlButton,
            isActive ? styles.pauseButton : styles.startButton,
          ]}
          onPress={handleToggle}
        >
          <Ionicons name={isActive ? 'pause' : 'play'} size={20} color="#fff" />
          <Text style={styles.controlButtonText}>
            {isActive ? 'Pause' : 'Start'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
    color: '#333',
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 6,
    marginLeft: 8,
  },
  timeContainer: {
    alignItems: 'center',
    marginVertical: 12,
  },
  timeText: {
    fontSize: 48,
    fontWeight: 'bold',
    fontVariant: ['tabular-nums'],
    color: '#333',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    marginVertical: 12,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
  },
  startButton: {
    backgroundColor: '#4CAF50',
  },
  pauseButton: {
    backgroundColor: '#FF9800',
  },
  resetButton: {
    backgroundColor: '#9E9E9E',
    marginRight: 8,
  },
  controlButtonText: {
    color: '#fff',
    marginLeft: 6,
    fontWeight: '600',
  },
});

export default TimerControls;
