// c:/Users/<USER>/Work/hackthon/mhh/components/ui/snackbar.tsx
import * as React from "react";
import Animated, { FadeInDown, FadeOutUp } from "react-native-reanimated";
import { Text } from "@/components/ui/text";
import { Button } from "@/components/ui/button";

type SnackbarAction = {
  label: string;
  onPress: () => void;
};

type SnackbarProps = {
  visible: boolean;
  message: string;
  onDismiss: () => void;
  action?: SnackbarAction;
  duration?: number;
};

export function Snackbar({
  visible,
  message,
  onDismiss,
  action,
  duration = 3000,
}: SnackbarProps) {
  React.useEffect(() => {
    if (!visible) return;
    const timer = setTimeout(() => {
      onDismiss();
    }, duration);
    return () => {
      clearTimeout(timer);
    };
  }, [visible, duration, onDismiss]);

  if (!visible) return null;

  return (
    <Animated.View
      entering={FadeInDown.duration(200)}
      exiting={FadeOutUp.duration(200)}
      className="absolute bottom-4 left-4 right-4 bg-card rounded-md p-4 flex-row items-center justify-between shadow-lg"
    >
      <Text className="text-foreground flex-1 mr-2">{message}</Text>
      {action && (
        <Button
          variant="ghost"
          size="sm"
          onPress={() => {
            action.onPress();
            onDismiss();
          }}
        >
          {action.label}
        </Button>
      )}
    </Animated.View>
  );
}

export type { SnackbarProps, SnackbarAction };
