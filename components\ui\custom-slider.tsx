import React from 'react';
import {
  View,
  PanResponder,
  StyleSheet,
  LayoutChangeEvent,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
  withTiming,
  useAnimatedReaction,
  useDerivedValue,
} from 'react-native-reanimated';

interface CustomSliderProps {
  min: number;
  max: number;
  value: number;
  onValueChange: (value: number) => void;
  trackColor?: string;
  thumbColor?: string;
  step?: number;
}

// Define the type for the data returned by the first callback of useAnimatedReaction
interface AnimatedReactionData {
  newTargetX: number;
  isDragging: boolean;
}

export const CustomSlider: React.FC<CustomSliderProps> = ({
  min,
  max,
  value,
  onValueChange,
  trackColor = '#e5e7eb',
  thumbColor = '#facc15',
  step = 1,
}) => {
  const trackWidth = useSharedValue(0);
  const thumbX = useSharedValue(0);
  const startThumbX = useSharedValue(0);
  const isDragging = useSharedValue(false);

  // Create shared values for the 'value', 'min', and 'max' props
  const animatedValue = useSharedValue(value);
  const animatedMin = useSharedValue(min);
  const animatedMax = useSharedValue(max);

  // Update shared values when their respective props change
  React.useEffect(() => {
    animatedValue.value = value;
  }, [value]);

  React.useEffect(() => {
    animatedMin.value = min;
  }, [min]);

  React.useEffect(() => {
    animatedMax.value = max;
  }, [max]);

  // Calculate the target thumb position based on animatedValue, animatedMin, animatedMax and trackWidth
  const targetThumbX = useDerivedValue(() => {
    if (trackWidth.value === 0) return 0;
    // Ensure animatedMax.value - animatedMin.value is not zero to avoid division by zero
    const range = animatedMax.value - animatedMin.value;
    if (range === 0) return 0; // Handle case where min and max are the same
    return ((animatedValue.value - animatedMin.value) / range) * trackWidth.value;
  }, [animatedValue, animatedMin, animatedMax, trackWidth]); // Dependencies for useDerivedValue

  // React to changes in targetThumbX and isDragging to update thumbX
  useAnimatedReaction(
    () => {
      return { newTargetX: targetThumbX.value, isDragging: isDragging.value };
    },
    (data: AnimatedReactionData, prevData: AnimatedReactionData | null) => {
      if (data.newTargetX !== prevData?.newTargetX) {
        if (!data.isDragging) {
          thumbX.value = withSpring(data.newTargetX);
        } else {
          // If dragging, update directly without spring to avoid conflict with pan responder
          thumbX.value = data.newTargetX;
        }
      }
    },
    [targetThumbX, isDragging] // Correct dependencies for useAnimatedReaction
  );

  const onTrackLayout = (e: LayoutChangeEvent) => {
    trackWidth.value = e.nativeEvent.layout.width;
    // The useAnimatedReaction will now handle updating thumbX based on trackWidth.value change
    // and the initial value.
  };

  const panResponder = React.useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        isDragging.value = true;
        startThumbX.value = thumbX.value;
      },
      onPanResponderMove: (_, gestureState) => {
        let newX = startThumbX.value + gestureState.dx;
        newX = Math.max(0, Math.min(newX, trackWidth.value));
        thumbX.value = newX;

        const percent = newX / trackWidth.value;
        let newValue = animatedMin.value + percent * (animatedMax.value - animatedMin.value); // Use animatedMin/Max
        newValue = Math.round(newValue / step) * step;
        runOnJS(onValueChange)(newValue);
      },
      onPanResponderRelease: () => {
        isDragging.value = false;
        // Snap to the final calculated position based on the value prop
        // Use animatedValue here to ensure it snaps to the latest prop value
        const finalThumbX = ((animatedValue.value - animatedMin.value) / (animatedMax.value - animatedMin.value)) * trackWidth.value; // Use animatedMin/Max
        thumbX.value = withSpring(finalThumbX);
      },
    }),
  ).current;

  const animatedThumbStyle = useAnimatedStyle(() => {
    const translateX = thumbX.value;
    // Ensure trackWidth.value is not zero to avoid division by zero
    const normalizedX = trackWidth.value > 0 ? translateX / trackWidth.value : 0;
    const curveHeight = 15;
    const translateY = -curveHeight * Math.pow(normalizedX - 0.5, 2);

    return {
      transform: [
        { translateX: translateX },
        { translateY: translateY },
        { scale: withSpring(isDragging.value ? 1.2 : 1) },
      ],
      backgroundColor: thumbColor,
    };
  });

  return (
    <View style={styles.container}>
      <View
        style={[styles.track, { backgroundColor: trackColor }]}
        onLayout={onTrackLayout}
      >
        <Animated.View
          style={[styles.thumb, animatedThumbStyle]}
          {...panResponder.panHandlers}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 40,
    justifyContent: 'center',
  },
  track: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    backgroundColor: '#e5e7eb',
    justifyContent: 'center',
  },
  thumb: {
    position: 'absolute',
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#facc15',
    borderWidth: 3,
    borderColor: '#fff',
    top: -10, // Adjust top to account for the curve
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
    shadowOffset: { width: 0, height: 2 },
  },
});
