import { query, mutation } from './_generated/server';
import { v } from 'convex/values';
import { Id } from './_generated/dataModel';

// Exercise Plan Queries
export const getExercisePlans = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    return await ctx.db
      .query('exercisePlan')
      .filter((q) => q.eq(q.field('userId'), identity.subject))
      .collect();
  },
});

export const getExercisePlanById = query({
  args: { id: v.id('exercisePlan') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const plan = await ctx.db.get(args.id);
    if (!plan) {
      throw new Error('Exercise plan not found');
    }

    if (plan.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    return plan;
  },
});

export const getExerciseStats = query({
  args: {
    startDate: v.string(),
    endDate: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get all sessions in the date range
    const sessions = await ctx.db
      .query('exerciseSession')
      .withIndex('by_user_date', (q) => q.eq('userId', identity.subject))
      .filter((q) =>
        q.and(
          q.gte(q.field('date'), args.startDate),
          q.lte(q.field('date'), args.endDate),
        ),
      )
      .collect();

    // Calculate statistics
    const stats = {
      totalSessions: sessions.length,
      completedSessions: sessions.filter((s) => s.completed).length,
      uniqueDays: new Set(sessions.map((s) => s.date)).size,
      exercisesByType: new Map<string, number>(),
    };

    // Count exercises by type
    sessions.forEach((session) => {
      session.exercises.forEach((exercise) => {
        const count = stats.exercisesByType.get(exercise.name) || 0;
        stats.exercisesByType.set(exercise.name, count + 1);
      });
    });

    return {
      ...stats,
      exercisesByType: Object.fromEntries(stats.exercisesByType),
    };
  },
});

// Exercise Plan Mutations
export const createExercisePlan = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    startDate: v.string(),
    endDate: v.string(),
    targetDaysPerWeek: v.number(),
    exercises: v.array(
      v.object({
        name: v.string(),
        sets: v.number(),
        reps: v.number(),
        weight: v.optional(v.number()),
        duration: v.optional(v.number()),
        restTime: v.optional(v.number()),
      }),
    ),
    healthPlanId: v.optional(v.id('healthPlan')),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Create the exercise plan
    const planId = await ctx.db.insert('exercisePlan', {
      ...args,
      userId: identity.subject,
      isActive: true,
    });

    return planId;
  },
});

export const updateExercisePlan = mutation({
  args: {
    id: v.id('exercisePlan'),
    updates: v.object({
      name: v.optional(v.string()),
      description: v.optional(v.string()),
      startDate: v.optional(v.string()),
      endDate: v.optional(v.string()),
      targetDaysPerWeek: v.optional(v.number()),
      exercises: v.optional(
        v.array(
          v.object({
            name: v.string(),
            sets: v.number(),
            reps: v.number(),
            weight: v.optional(v.number()),
            duration: v.optional(v.number()),
            restTime: v.optional(v.number()),
          }),
        ),
      ),
      isActive: v.optional(v.boolean()),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const plan = await ctx.db.get(args.id);
    if (!plan) {
      throw new Error('Exercise plan not found');
    }

    if (plan.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    await ctx.db.patch(args.id, args.updates);
    return { success: true };
  },
});

export const getCompletedSessionsCount = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const sessions = await ctx.db
      .query('exerciseSession')
      .filter((q) =>
        q.and(
          q.eq(q.field('userId'), identity.subject),
          q.eq(q.field('completed'), true),
        ),
      )
      .collect();
    return sessions.length;
  },
});

export const deleteExercisePlan = mutation({
  args: { id: v.id('exercisePlan') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const plan = await ctx.db.get(args.id);
    if (!plan) {
      throw new Error('Exercise plan not found');
    }

    if (plan.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    await ctx.db.delete(args.id);
    return { success: true };
  },
});

// Exercise Session Mutations
export const createExerciseSession = mutation({
  args: {
    planId: v.id('exercisePlan'),
    date: v.string(),
    exercises: v.array(
      v.object({
        name: v.string(),
        sets: v.array(
          v.object({
            reps: v.number(),
            weight: v.optional(v.number()),
            duration: v.optional(v.number()),
          }),
        ),
        notes: v.optional(v.string()),
      }),
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const plan = await ctx.db.get(args.planId);
    if (!plan) {
      throw new Error('Exercise plan not found');
    }

    if (plan.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    const sessionId = await ctx.db.insert('exerciseSession', {
      ...args,
      userId: identity.subject,
      completed: false,
    });

    return sessionId;
  },
});

export const updateExerciseSession = mutation({
  args: {
    id: v.id('exerciseSession'),
    updates: v.object({
      exercises: v.optional(
        v.array(
          v.object({
            name: v.string(),
            sets: v.array(
              v.object({
                reps: v.number(),
                weight: v.optional(v.number()),
                duration: v.optional(v.number()),
              }),
            ),
            notes: v.optional(v.string()),
          }),
        ),
      ),
      completed: v.optional(v.boolean()),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const session = await ctx.db.get(args.id);
    if (!session) {
      throw new Error('Exercise session not found');
    }

    if (session.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    await ctx.db.patch(args.id, args.updates);
    return { success: true };
  },
});
