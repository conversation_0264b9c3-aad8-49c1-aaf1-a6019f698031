import { mutation, query } from './_generated/server';
import { v } from 'convex/values';
import { Id } from './_generated/dataModel';

// Mutations
export const createMeditationSession = mutation({
  args: {
    date: v.string(),
    type: v.string(),
    durationMinutes: v.number(),
    completed: v.boolean(),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const sessionId = await ctx.db.insert('meditationSession', { userId, ...args });
    return sessionId;
  },
});

export const updateMeditationSession = mutation({
  args: {
    id: v.id('meditationSession'),
    updates: v.object({
      type: v.optional(v.string()),
      durationMinutes: v.optional(v.number()),
      completed: v.optional(v.boolean()),
      notes: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const session = await ctx.db.get(args.id);
    if (!session) {
      throw new Error('Meditation session not found');
    }

    if (session.userId !== userId) {
      throw new Error('Unauthorized');
    }

    await ctx.db.patch(args.id, args.updates);
    return { success: true };
  },
});

export const deleteMeditationSession = mutation({
  args: { id: v.id('meditationSession') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const session = await ctx.db.get(args.id);
    if (!session) {
      throw new Error('Meditation session not found');
    }

    if (session.userId !== userId) {
      throw new Error('Unauthorized');
    }

    await ctx.db.delete(args.id);
    return { success: true };
  },
});

// Queries
export const getMeditationSessions = query({
  args: {
    startDate: v.optional(v.string()),
    endDate: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    let query = ctx.db
      .query('meditationSession')
      .filter((q) => q.eq(q.field('userId'), userId));

    if (args.startDate) {
      query = query.filter((q) => q.gte(q.field('date'), args.startDate!));
    }

    if (args.endDate) {
      query = query.filter((q) => q.lte(q.field('date'), args.endDate!));
    }

    return await query.order('desc').collect();
  },
});

export const getMeditationSessionById = query({
  args: { id: v.id('meditationSession') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const session = await ctx.db.get(args.id);
    if (!session) {
      throw new Error('Meditation session not found');
    }

    if (session.userId !== userId) {
      throw new Error('Unauthorized');
    }

    return session;
  },
});

export const getMeditationStats = query({
  args: {
    startDate: v.optional(v.string()),
    endDate: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const startDate = args.startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const endDate = args.endDate || new Date().toISOString().split('T')[0];

    const sessions = await ctx.db
      .query('meditationSession')
      .filter((q) => q.eq(q.field('userId'), userId))
      .filter((q) =>
        q.and(
          q.gte(q.field('date'), startDate),
          q.lte(q.field('date'), endDate),
        ),
      )
      .collect();

    // Calculate statistics
    const totalSessions = sessions.length;
    const totalMinutes = sessions.reduce((sum, session) => sum + session.durationMinutes, 0);
    const completedSessions = sessions.filter(session => session.completed).length;
    const sessionsByType = sessions.reduce((acc, session) => {
      acc[session.type] = (acc[session.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalSessions,
      totalMinutes,
      completedSessions,
      completionRate: totalSessions > 0 ? (completedSessions / totalSessions) * 100 : 0,
      sessionsByType,
      averageDuration: totalSessions > 0 ? totalMinutes / totalSessions : 0,
    };
  },
});