import React, { useState } from 'react';
import { View, SafeAreaView, ImageBackground } from 'react-native';
import { useMutation, useQuery } from 'convex/react';
import { useRouter } from 'expo-router';
import { api } from '../../convex/_generated/api';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Spinner } from '@/components/ui/spinner';
import { Snackbar } from '@/components/ui/snackbar';
import { Progress } from '@/components/ui/progress';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import Animated, { FadeIn, FadeInDown } from 'react-native-reanimated';
import LottieView from 'lottie-react-native';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

const TOTAL_STEPS = 5;

const PREDEFINED_ACTIVITY_LEVELS = [
  { label: 'Sedentary', value: 'sedentary', icon: 'couch' },
  { label: 'Lightly Active', value: 'lightly_active', icon: 'walk' },
  { label: 'Moderately Active', value: 'moderately_active', icon: 'run' },
  { label: 'Very Active', value: 'very_active', icon: 'bike' },
  { label: 'Extra Active', value: 'extra_active', icon: 'dumbbell' },
];

const OnboardingScreen = () => {
  const [step, setStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Profile State
  const [dateOfBirth, setDateOfBirth] = useState('');
  const [weight, setWeight] = useState('');
  const [height, setHeight] = useState('');
  const [gender, setGender] = useState<string | undefined>(undefined);
  const [activityLevel, setActivityLevel] = useState<string | undefined>(
    undefined,
  );
  const [targetWeight, setTargetWeight] = useState('');
  const [targetCalories, setTargetCalories] = useState('');

  const userProfile = useQuery(api.userProfile.getProfile);
  const createProfileMutation = useMutation(api.userProfile.createProfile);
  const updateProfileMutation = useMutation(api.userProfile.updateProfile);

  const router = useRouter();

  const handleNext = async () => {
    Haptics.selectionAsync();
    if (step < TOTAL_STEPS) {
      setStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    Haptics.selectionAsync();
    if (step > 0) {
      setStep((prev) => prev - 1);
    }
  };

  const handleSaveProfile = async () => {
    setIsLoading(true);
    try {
      const profileData = {
        dateOfBirth: dateOfBirth || undefined,
        weight: weight ? parseFloat(weight) : undefined,
        height: height ? parseFloat(height) : undefined,
        gender: gender as 'male' | 'female' | 'other' | undefined,
        activityLevel: activityLevel as
          | 'sedentary'
          | 'lightly_active'
          | 'moderately_active'
          | 'very_active'
          | 'extra_active'
          | undefined,
        targetWeight: targetWeight ? parseFloat(targetWeight) : undefined,
        targetCalories: targetCalories ? parseFloat(targetCalories) : undefined,
        hasCompletedOnboarding: true,
      };

      if (userProfile) {
        await updateProfileMutation(profileData);
      } else {
        await createProfileMutation(profileData);
      }

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      setSnackbarMessage('Profile saved successfully! 🎉');
      setShowSnackbar(true);
      setTimeout(() => {
        router.replace('/(tabs)');
      }, 1500);
    } catch (error) {
      console.error('Failed to save profile:', error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      setSnackbarMessage('Failed to save profile. Please try again.');
      setShowSnackbar(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = async () => {
    setIsLoading(true);
    try {
      const profileData = {
        hasCompletedOnboarding: true,
      };

      if (userProfile) {
        await updateProfileMutation(profileData);
      } else {
        await createProfileMutation(profileData);
      }
      router.replace('/(tabs)');
    } catch (error) {
      console.error('Failed to skip onboarding:', error);
      setSnackbarMessage('Failed to skip onboarding. Please try again.');
      setShowSnackbar(true);
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (step) {
      case 0:
        return (
          <Animated.View
            entering={FadeIn.duration(800)}
            className="flex-1 items-center justify-center p-6 bg-background/80"
          >
            <View className="items-center justify-center">
              <LottieView
                source={require('../../assets/animations/cook.json')}
                autoPlay
                loop
                style={{ width: 250, height: 250 }}
              />
              <Text className="text-4xl font-extrabold text-center mt-8 mb-4 text-foreground">
                Welcome to SayMhhh!
              </Text>
              <Text className="text-lg text-center text-muted-foreground mb-12">
                Your journey to a healthier, happier you starts now.
              </Text>
              <Button
                variant="default"
                size="lg"
                onPress={handleNext}
                className="rounded-full w-full mb-4 shadow-lg"
              >
                <Text className="text-lg font-bold text-primary-foreground">
                  Get Started
                </Text>
              </Button>
              <Button variant="link" onPress={handleSkip} className="w-full">
                <Text className="text-muted-foreground">Skip for now</Text>
              </Button>
            </View>
          </Animated.View>
        );
      case 1:
        return (
          <Animated.View
            entering={FadeInDown.duration(500).springify()}
            className="w-full"
          >
            <Card className="w-full bg-card/90 border-border/20 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-center text-foreground">
                  About You
                </CardTitle>
              </CardHeader>
              <CardContent>
                <View className="mb-6">
                  <Label className="mb-2 text-lg text-muted-foreground">
                    Date of Birth (YYYY-MM-DD)
                  </Label>
                  <Input
                    placeholder="e.g., 1990-01-15"
                    value={dateOfBirth}
                    onChangeText={setDateOfBirth}
                    keyboardType="numbers-and-punctuation"
                    className="text-lg"
                  />
                </View>

                <View>
                  <Label className="mb-3 text-lg text-muted-foreground">
                    Gender
                  </Label>
                  <ToggleGroup
                    type="single"
                    value={gender}
                    onValueChange={setGender}
                    className="justify-around"
                  >
                    <ToggleGroupItem
                      value="male"
                      className="flex-1 p-4 rounded-lg"
                    >
                      <MaterialCommunityIcons
                        name="gender-male"
                        size={24}
                        color="#3b82f6"
                      />
                      <Text className="ml-2 text-lg">Male</Text>
                    </ToggleGroupItem>
                    <ToggleGroupItem
                      value="female"
                      className="flex-1 p-4 rounded-lg"
                    >
                      <MaterialCommunityIcons
                        name="gender-female"
                        size={24}
                        color="#ec4899"
                      />
                      <Text className="ml-2 text-lg">Female</Text>
                    </ToggleGroupItem>
                    <ToggleGroupItem
                      value="other"
                      className="flex-1 p-4 rounded-lg"
                    >
                      <MaterialCommunityIcons
                        name="gender-transgender"
                        size={24}
                        color="#8b5cf6"
                      />
                      <Text className="ml-2 text-lg">Other</Text>
                    </ToggleGroupItem>
                  </ToggleGroup>
                </View>
              </CardContent>
            </Card>
          </Animated.View>
        );
      case 2:
        return (
          <Animated.View
            entering={FadeInDown.duration(500).springify()}
            className="w-full"
          >
            <Card className="w-full bg-card/90 border-border/20 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-center text-foreground">
                  Physical Stats
                </CardTitle>
              </CardHeader>
              <CardContent>
                <View className="mb-6">
                  <Label className="mb-2 text-lg text-muted-foreground">
                    Weight (kg)
                  </Label>
                  <Input
                    placeholder="e.g., 70"
                    value={weight}
                    onChangeText={setWeight}
                    keyboardType="numeric"
                    className="text-lg"
                  />
                </View>
                <View>
                  <Label className="mb-2 text-lg text-muted-foreground">
                    Height (cm)
                  </Label>
                  <Input
                    placeholder="e.g., 175"
                    value={height}
                    onChangeText={setHeight}
                    keyboardType="numeric"
                    className="text-lg"
                  />
                </View>
              </CardContent>
            </Card>
          </Animated.View>
        );
      case 3:
        return (
          <Animated.View
            entering={FadeInDown.duration(500).springify()}
            className="w-full"
          >
            <Card className="w-full bg-card/90 border-border/20 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-center text-foreground">
                  Activity Level
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ToggleGroup
                  type="single"
                  value={activityLevel}
                  onValueChange={setActivityLevel}
                  className="flex-col"
                >
                  {PREDEFINED_ACTIVITY_LEVELS.map((level) => (
                    <ToggleGroupItem
                      key={level.value}
                      value={level.value}
                      className="w-full justify-start p-4 rounded-lg mb-2"
                    >
                      <MaterialCommunityIcons
                        name={level.icon as any}
                        size={24}
                        className="text-foreground"
                      />
                      <Text className="ml-4 text-lg">{level.label}</Text>
                    </ToggleGroupItem>
                  ))}
                </ToggleGroup>
              </CardContent>
            </Card>
          </Animated.View>
        );
      case 4:
        return (
          <Animated.View
            entering={FadeInDown.duration(500).springify()}
            className="w-full"
          >
            <Card className="w-full bg-card/90 border-border/20 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-center text-foreground">
                  Your Goals
                </CardTitle>
              </CardHeader>
              <CardContent>
                <View className="mb-6">
                  <Label className="mb-2 text-lg text-muted-foreground">
                    Target Weight (kg)
                  </Label>
                  <Input
                    placeholder="e.g., 65"
                    value={targetWeight}
                    onChangeText={setTargetWeight}
                    keyboardType="numeric"
                    className="text-lg"
                  />
                </View>
                <View>
                  <Label className="mb-2 text-lg text-muted-foreground">
                    Daily Calorie Goal
                  </Label>
                  <Input
                    placeholder="e.g., 2000"
                    value={targetCalories}
                    onChangeText={setTargetCalories}
                    keyboardType="numeric"
                    className="text-lg"
                  />
                </View>
              </CardContent>
            </Card>
          </Animated.View>
        );
      case 5:
        return (
          <Animated.View
            entering={FadeIn.duration(800)}
            className="w-full items-center"
          >
            <Card className="w-full bg-card/90 border-border/20 shadow-lg">
              <CardHeader>
                <CardTitle className="text-3xl font-bold text-center text-foreground">
                  You're All Set!
                </CardTitle>
              </CardHeader>
              <CardContent className="items-center">
                <Text className="text-lg text-center text-muted-foreground mb-8">
                  Ready to start your health journey?
                </Text>
                <Button
                  size="lg"
                  onPress={handleSaveProfile}
                  disabled={isLoading}
                  className="w-full rounded-full shadow-lg"
                >
                  {isLoading ? (
                    <Spinner />
                  ) : (
                    <Text className="text-lg font-bold text-primary-foreground">
                      Let's Go!
                    </Text>
                  )}
                </Button>
              </CardContent>
            </Card>
          </Animated.View>
        );
      default:
        return null;
    }
  };

  return (
    <ImageBackground
      source={require('../../assets/images/auth.jpg')}
      className="flex-1"
      resizeMode="cover"
    >
      <SafeAreaView className="flex-1 bg-black/50">
        <View className="flex-1 items-center justify-center p-6">
          {step > 0 && (
            <View className="w-full mb-6">
              <Progress value={(step / TOTAL_STEPS) * 100} className="h-2" />
              <Text className="text-center text-sm text-white mt-2">
                Step {step} of {TOTAL_STEPS}
              </Text>
            </View>
          )}

          {renderStep()}

          {step > 0 && step < TOTAL_STEPS && (
            <View className="flex-row w-full justify-between mt-6">
              <Button
                variant="outline"
                onPress={handleBack}
                className="rounded-full"
              >
                <Text>Back</Text>
              </Button>
              <Button onPress={handleNext} className="rounded-full">
                <Text className="text-primary-foreground">Next</Text>
              </Button>
            </View>
          )}
        </View>

        <Snackbar
          message={snackbarMessage}
          visible={showSnackbar}
          onDismiss={() => setShowSnackbar(false)}
          duration={3000}
        />
      </SafeAreaView>
    </ImageBackground>
  );
};

export default OnboardingScreen;
