import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import {
  Moon,
  Heart,
  Brain,
  TrendingUp,
  Calendar,
  Target,
  Smile,
  Activity,
  ChevronRight,
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Bar<PERSON><PERSON>, LineChart } from 'react-native-gifted-charts';

const { width } = Dimensions.get('window');

export default function WellbeingDashboard() {
  const router = useRouter();
  
  // Get recent data
  const recentMoodEntries = useQuery(api.mood.getMoodEntries, {
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });

  const sleepSummary = useQuery(api.sleep.getLast7DaysSleepSummary);
  const lastSleepEntry = useQuery(api.sleep.getLastSleepEntry);
  const currentMood = useQuery(api.mood.getCurrentMood);
  const moodSummary = useQuery(api.mood.getMoodSummary, {
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });

  const averageSleepDuration = useQuery(api.sleep.getAverageSleepDuration, {
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });

  // Calculate wellbeing score
  const calculateWellbeingScore = () => {
    let score = 0;
    let factors = 0;

    // Sleep factor (40% weight)
    if (averageSleepDuration !== undefined) {
      const sleepScore = Math.min(100, (averageSleepDuration / 8) * 100);
      score += sleepScore * 0.4;
      factors++;
    }

    // Mood factor (40% weight)
    if (moodSummary?.averageIntensity) {
      const moodScore = (moodSummary.averageIntensity / 10) * 100;
      score += moodScore * 0.4;
      factors++;
    }

    // Activity factor (20% weight)
    const activityScore = 75; // Placeholder
    score += activityScore * 0.2;
    factors++;

    return factors > 0 ? Math.round(score / factors) : 0;
  };

  const wellbeingScore = calculateWellbeingScore();

  const getMoodColor = (mood: string) => {
    const colors: Record<string, string> = {
      happy: '#22c55e',
      excited: '#f59e0b',
      calm: '#06b6d4',
      neutral: '#6b7280',
      tired: '#8b5cf6',
      stressed: '#ef4444',
      anxious: '#f97316',
      sad: '#3b82f6',
      angry: '#dc2626',
      other: '#64748b',
    };
    return colors[mood] || '#6b7280';
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return '#22c55e';
    if (score >= 60) return '#f59e0b';
    return '#ef4444';
  };

  const QuickActionCard = ({ 
    title, 
    subtitle, 
    icon: Icon, 
    color, 
    onPress 
  }: {
    title: string;
    subtitle: string;
    icon: any;
    color: string;
    onPress: () => void;
  }) => (
    <TouchableOpacity
      onPress={onPress}
      className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 mx-1"
    >
      <View className="flex-row items-center justify-between mb-3">
        <View className={`w-10 h-10 rounded-xl items-center justify-center`} style={{ backgroundColor: `${color}20` }}>
          <Icon size={20} color={color} />
        </View>
        <ChevronRight size={16} color="#9ca3af" />
      </View>
      <Text className="text-gray-900 font-semibold text-base mb-1">{title}</Text>
      <Text className="text-gray-500 text-sm">{subtitle}</Text>
    </TouchableOpacity>
  );

  const MetricCard = ({ 
    title, 
    value, 
    unit, 
    trend, 
    icon: Icon, 
    color 
  }: {
    title: string;
    value: string | number;
    unit?: string;
    trend?: number;
    icon: any;
    color: string;
  }) => (
    <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 mx-1">
      <View className="flex-row items-center justify-between mb-3">
        <View className={`w-8 h-8 rounded-lg items-center justify-center`} style={{ backgroundColor: `${color}20` }}>
          <Icon size={16} color={color} />
        </View>
        {trend !== undefined && (
          <View className="flex-row items-center">
            <TrendingUp size={12} color={trend > 0 ? '#22c55e' : '#ef4444'} />
            <Text className={`text-xs ml-1 ${trend > 0 ? 'text-green-500' : 'text-red-500'}`}>
              {trend > 0 ? '+' : ''}{trend}%
            </Text>
          </View>
        )}
      </View>
      <Text className="text-2xl font-bold text-gray-900 mb-1">
        {value}{unit && <Text className="text-lg text-gray-500">{unit}</Text>}
      </Text>
      <Text className="text-gray-500 text-sm">{title}</Text>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
        {/* Header */}
        <View className="px-6 pt-6 pb-4">
          <Text className="text-3xl font-bold text-gray-900 mb-2">Wellbeing</Text>
          <Text className="text-gray-600">Track your mental and physical wellness</Text>
        </View>

        {/* Wellbeing Score Card */}
        <View className="mx-6 mb-6">
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            className="rounded-3xl p-6"
          >
            <View className="flex-row items-center justify-between">
              <View className="flex-1">
                <Text className="text-white text-lg font-semibold mb-2">
                  Wellbeing Score
                </Text>
                <Text className="text-white text-4xl font-bold mb-1">
                  {wellbeingScore}
                </Text>
                <Text className="text-white/80 text-sm">
                  {wellbeingScore >= 80 ? 'Excellent' : wellbeingScore >= 60 ? 'Good' : 'Needs Attention'}
                </Text>
              </View>
              <View className="w-20 h-20 rounded-full bg-white/20 items-center justify-center">
                <Activity size={32} color="white" />
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Quick Metrics */}
        <View className="px-6 mb-6">
          <Text className="text-xl font-semibold text-gray-900 mb-4">Today's Overview</Text>
          <View className="flex-row">
            <MetricCard
              title="Sleep Quality"
              value={lastSleepEntry?.qualityRating || 0}
              unit="/5"
              trend={5}
              icon={Moon}
              color="#8b5cf6"
            />
            <MetricCard
              title="Current Mood"
              value={currentMood?.mood ? currentMood.mood.charAt(0).toUpperCase() + currentMood.mood.slice(1) : 'Not set'}
              icon={Smile}
              color={currentMood?.mood ? getMoodColor(currentMood.mood) : '#6b7280'}
            />
          </View>
        </View>

        {/* Sleep Chart */}
        {sleepSummary && sleepSummary.length > 0 && (
          <View className="px-6 mb-6">
            <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
              <Text className="text-lg font-semibold text-gray-900 mb-4">Sleep Patterns</Text>
              <BarChart
                data={sleepSummary.map(item => ({
                  value: item.value,
                  label: item.label,
                  frontColor: '#8b5cf6',
                }))}
                width={width - 80}
                height={180}
                barWidth={32}
                spacing={20}
                roundedTop
                roundedBottom
                hideRules
                xAxisThickness={0}
                yAxisThickness={0}
                yAxisTextStyle={{ color: '#9ca3af', fontSize: 12 }}
                xAxisLabelTextStyle={{ color: '#9ca3af', fontSize: 12 }}
                noOfSections={4}
                maxValue={10}
              />
            </View>
          </View>
        )}

        {/* Mood Trends */}
        {recentMoodEntries && recentMoodEntries.length > 0 && (
          <View className="px-6 mb-6">
            <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
              <Text className="text-lg font-semibold text-gray-900 mb-4">Mood Trends</Text>
              <LineChart
                data={recentMoodEntries.slice(-7).map((entry, index) => ({
                  value: entry.intensity || 5,
                  label: new Date(entry.date).toLocaleDateString('en-US', { weekday: 'short' }),
                }))}
                width={width - 80}
                height={160}
                color="#f59e0b"
                thickness={3}
                dataPointsColor="#f59e0b"
                dataPointsRadius={6}
                hideRules
                xAxisThickness={0}
                yAxisThickness={0}
                yAxisTextStyle={{ color: '#9ca3af', fontSize: 12 }}
                xAxisLabelTextStyle={{ color: '#9ca3af', fontSize: 12 }}
                curved
                areaChart
                startFillColor="#f59e0b"
                startOpacity={0.3}
                endOpacity={0.1}
              />
            </View>
          </View>
        )}

        {/* Quick Actions */}
        <View className="px-6 mb-6">
          <Text className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</Text>
          <View className="flex-row mb-3">
            <QuickActionCard
              title="Log Sleep"
              subtitle="Track last night"
              icon={Moon}
              color="#8b5cf6"
              onPress={() => router.push('/wellbeing/sleep')}
            />
            <QuickActionCard
              title="Log Mood"
              subtitle="How are you feeling?"
              icon={Heart}
              color="#f59e0b"
              onPress={() => router.push('/wellbeing/mood')}
            />
          </View>
          <View className="flex-row">
            <QuickActionCard
              title="Meditate"
              subtitle="Find your calm"
              icon={Brain}
              color="#06b6d4"
              onPress={() => router.push('/wellbeing/meditation')}
            />
            <QuickActionCard
              title="AI Insights"
              subtitle="Personalized tips"
              icon={TrendingUp}
              color="#22c55e"
              onPress={() => router.push('/wellbeing/insights')}
            />
          </View>
        </View>

        {/* Weekly Summary */}
        <View className="px-6 mb-8">
          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
            <Text className="text-lg font-semibold text-gray-900 mb-4">This Week</Text>
            <View className="space-y-3">
              <View className="flex-row items-center justify-between">
                <Text className="text-gray-600">Average Sleep</Text>
                <Text className="font-semibold text-gray-900">
                  {averageSleepDuration?.toFixed(1) || '0'}h
                </Text>
              </View>
              <View className="flex-row items-center justify-between">
                <Text className="text-gray-600">Mood Entries</Text>
                <Text className="font-semibold text-gray-900">
                  {recentMoodEntries?.length || 0}
                </Text>
              </View>
              <View className="flex-row items-center justify-between">
                <Text className="text-gray-600">Meditation Sessions</Text>
                <Text className="font-semibold text-gray-900">0</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}