import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { theme } from '@/constants/theme';
import {
  Calendar,
  Dumbbell,
  TrendingUp,
  Target,
  Plus,
} from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import Animated, { FadeInDown } from 'react-native-reanimated';

const AnimatedView = Animated.createAnimatedComponent(View);

export default function FitnessDashboard() {
  const router = useRouter();
  const workouts =
    useQuery(api.fitness.getWorkouts, { completed: false }) || [];
  const weeklyStats = useQuery(api.fitness.getWeeklyStats) || {
    completedWorkouts: 0,
    totalPlanned: 0,
    streakDays: 0,
    caloriesBurned: 0,
  };

  const upcomingWorkouts = workouts
    .filter((w) => !w.completed && new Date(w.scheduledDate) >= new Date())
    .slice(0, 3);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <AnimatedView style={styles.header} entering={FadeInDown.delay(100)}>
          <Text style={styles.title}>Fitness Dashboard</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => router.push('/fitness/create-workout')}
          >
            <Plus size={24} color={theme.colors.white} />
          </TouchableOpacity>
        </AnimatedView>

        {/* Weekly Stats */}
        <AnimatedView style={styles.statsCard} entering={FadeInDown.delay(200)}>
          <Text style={styles.statsTitle}>This Week's Progress</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Dumbbell size={24} color={theme.colors.primary} />
              <Text style={styles.statValue}>
                {weeklyStats.completedWorkouts}/{weeklyStats.totalPlanned}
              </Text>
              <Text style={styles.statLabel}>Workouts</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Target size={24} color={theme.colors.success} />
              <Text style={styles.statValue}>{weeklyStats.streakDays}</Text>
              <Text style={styles.statLabel}>Day Streak</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <TrendingUp size={24} color={theme.colors.accent} />
              <Text style={styles.statValue}>{weeklyStats.caloriesBurned}</Text>
              <Text style={styles.statLabel}>Calories</Text>
            </View>
          </View>
        </AnimatedView>

        {/* Quick Actions */}
        <AnimatedView
          style={styles.quickActions}
          entering={FadeInDown.delay(300)}
        >
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionGrid}>
            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => router.push('/fitness/schedule')}
            >
              <Calendar size={32} color={theme.colors.primary} />
              <Text style={styles.actionTitle}>Schedule</Text>
              <Text style={styles.actionSubtitle}>View workout calendar</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => router.push('/fitness/templates')}
            >
              <Dumbbell size={32} color={theme.colors.secondary} />
              <Text style={styles.actionTitle}>Templates</Text>
              <Text style={styles.actionSubtitle}>Pre-made workouts</Text>
            </TouchableOpacity>
          </View>
        </AnimatedView>

        {/* Upcoming Workouts */}
        <AnimatedView
          style={styles.upcomingSection}
          entering={FadeInDown.delay(400)}
        >
          <Text style={styles.sectionTitle}>Upcoming Workouts</Text>
          {upcomingWorkouts.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={styles.emptyText}>
                No upcoming workouts scheduled
              </Text>
              <TouchableOpacity
                style={styles.scheduleButton}
                onPress={() => router.push('/fitness/create-workout')}
              >
                <Text style={styles.scheduleButtonText}>Schedule Workout</Text>
              </TouchableOpacity>
            </View>
          ) : (
            upcomingWorkouts.map((workout, index) => (
              <TouchableOpacity
                key={workout._id}
                style={styles.workoutCard}
                onPress={() => router.push(`/fitness/workout/${workout._id}`)}
              >
                <View style={styles.workoutHeader}>
                  <Text style={styles.workoutTitle}>{workout.name}</Text>
                  <Text style={styles.workoutDate}>
                    {new Date(workout.scheduledDate).toLocaleDateString()}
                  </Text>
                </View>
                <Text style={styles.workoutType}>{workout.type}</Text>
                <Text style={styles.workoutDuration}>
                  {workout.estimatedDuration} min
                </Text>
              </TouchableOpacity>
            ))
          )}
        </AnimatedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  content: {
    flex: 1,
    padding: theme.spacing.l,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  title: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xxl,
    color: theme.colors.gray[900],
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginBottom: theme.spacing.xl,
    ...theme.shadows.medium,
  },
  statsTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.l,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.gray[900],
    marginTop: theme.spacing.s,
  },
  statLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    marginTop: theme.spacing.xs,
  },
  statDivider: {
    width: 1,
    height: '80%',
    backgroundColor: theme.colors.gray[200],
  },
  quickActions: {
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.l,
  },
  actionGrid: {
    flexDirection: 'row',
    gap: theme.spacing.l,
  },
  actionCard: {
    flex: 1,
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    alignItems: 'center',
    ...theme.shadows.small,
  },
  actionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
    marginTop: theme.spacing.m,
  },
  actionSubtitle: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    marginTop: theme.spacing.xs,
    textAlign: 'center',
  },
  upcomingSection: {
    marginBottom: theme.spacing.xl,
  },
  emptyState: {
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  emptyText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
    marginBottom: theme.spacing.l,
  },
  scheduleButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.m,
    paddingHorizontal: theme.spacing.xl,
    borderRadius: theme.borderRadius.m,
  },
  scheduleButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
  },
  workoutCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginBottom: theme.spacing.m,
    ...theme.shadows.small,
  },
  workoutHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.s,
  },
  workoutTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
  },
  workoutDate: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
  },
  workoutType: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.primary,
    marginBottom: theme.spacing.xs,
  },
  workoutDuration: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
  },
});
