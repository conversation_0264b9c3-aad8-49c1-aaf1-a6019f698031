import { useMutation, useAction } from 'convex/react';
import { api } from '../convex/_generated/api';
import { Id } from '../convex/_generated/dataModel';

type UploadProgress = {
  loaded: number;
  total: number;
  percentage: number;
};

type UploadOptions = {
  onProgress?: (progress: UploadProgress) => void;
  maxFileSize?: number; // in bytes
  allowedTypes?: string[]; // e.g., ['image/jpeg', 'image/png']
};

export const useImageUpload = () => {
  const generateUploadUrl = useMutation(api.uploads.generateUploadUrl);
  const getImageUrl = useAction(api.uploads.getImageUrl);

  const validateFile = (file: Blob, options: UploadOptions = {}) => {
    const { maxFileSize = 5 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/gif'] } = options;

    if (file.size > maxFileSize) {
      throw new Error(`File size exceeds the limit of ${maxFileSize / 1024 / 1024}MB`);
    }

    if (!allowedTypes.includes(file.type)) {
      throw new Error(`File type ${file.type} is not supported. Please upload one of: ${allowedTypes.join(', ')}`);
    }
  };

  const uploadImage = async (file: Blob, options: UploadOptions = {}): Promise<string> => {
    try {
      // 1. Validate the file
      validateFile(file, options);

      // 2. Get a signed upload URL from Convex
      const uploadUrl = await generateUploadUrl();
      
      // 3. Upload the file to the signed URL with progress tracking
      const xhr = new XMLHttpRequest();
      
      return new Promise((resolve, reject) => {
        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable && options.onProgress) {
            const progress = {
              loaded: event.loaded,
              total: event.total,
              percentage: Math.round((event.loaded / event.total) * 100)
            };
            options.onProgress(progress);
          }
        };

        xhr.onload = async () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const storageId = xhr.responseText as Id<'_storage'>;
              if (!storageId) {
                throw new Error('No storageId received from upload');
              }
              const imageUrl = await getImageUrl({ storageId });
              if (!imageUrl) {
                throw new Error('Failed to get image URL');
              }
              resolve(imageUrl);
            } catch (error) {
              reject(error);
            }
          } else {
            reject(new Error(`Upload failed with status ${xhr.status}: ${xhr.statusText}`));
          }
        };

        xhr.onerror = () => {
          reject(new Error('Upload failed. Please check your network connection.'));
        };

        xhr.open('PUT', uploadUrl);
        xhr.setRequestHeader('Content-Type', file.type);
        xhr.send(file);
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error; // Re-throw to allow error handling in the component
    }
  };

  return { uploadImage };
};
