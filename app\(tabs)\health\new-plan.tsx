import { ScrollView, StyleSheet, View } from 'react-native';
import React from 'react';
import { Text } from '@/components/ui/text';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Header } from '@/components/ui/Header';
import { CreateHealthPlanForm } from '@/components/health/CreateHealthPlanForm';
import { router } from 'expo-router';

const NewHealthPlan = () => {
  return (
    <SafeAreaView edges={['top']} className="flex-1">
      <Header showBackButton title="" />
      <CreateHealthPlanForm
        onPlanCreated={() => router.replace('/health')}
        onCancel={() => router.replace('/health')}
      />
    </SafeAreaView>
  );
};

export default NewHealthPlan;

const styles = StyleSheet.create({});
