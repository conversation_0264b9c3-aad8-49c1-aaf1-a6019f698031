import { v } from 'convex/values';
import { mutation, query } from './_generated/server';

export const getProfile = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    return ctx.db
      .query('userProfile')
      .withIndex('by_user', (q) => q.eq('userId', identity.subject))
      .first();
  },
});

export const createProfile = mutation({
  args: {
    dateOfBirth: v.optional(v.string()),
    weight: v.optional(v.number()),
    height: v.optional(v.number()),
    gender: v.optional(
      v.union(v.literal('male'), v.literal('female'), v.literal('other')),
    ),
    activityLevel: v.optional(
      v.union(
        v.literal('sedentary'),
        v.literal('lightly_active'),
        v.literal('moderately_active'),
        v.literal('very_active'),
        v.literal('extra_active'),
      ),
    ),
    targetWeight: v.optional(v.number()),
    targetCalories: v.optional(v.number()),
    hasCompletedOnboarding: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('User not authenticated');
    }

    return ctx.db.insert('userProfile', {
      userId: identity.subject,
      ...args,
    });
  },
});

export const updateProfile = mutation({
  args: {
    dateOfBirth: v.optional(v.string()),
    weight: v.optional(v.number()),
    height: v.optional(v.number()),
    gender: v.optional(
      v.union(v.literal('male'), v.literal('female'), v.literal('other')),
    ),
    activityLevel: v.optional(
      v.union(
        v.literal('sedentary'),
        v.literal('lightly_active'),
        v.literal('moderately_active'),
        v.literal('very_active'),
        v.literal('extra_active'),
      ),
    ),
    targetWeight: v.optional(v.number()),
    targetCalories: v.optional(v.number()),
    hasCompletedOnboarding: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('User not authenticated');
    }

    const existingProfile = await ctx.db
      .query('userProfile')
      .withIndex('by_user', (q) => q.eq('userId', identity.subject))
      .first();

    if (!existingProfile) {
      throw new Error('User profile not found. Please create one first.');
    }

    await ctx.db.patch(existingProfile._id, args);
  },
});
