import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card } from '../ui/card';
import { Text } from '../ui/text';
import { Avatar } from '../ui/avatar';
import { Button } from '../ui/button';
import { Droplet, Target, Dumbbell } from 'lucide-react-native';

export function WaterReminderCard() {
  return (
    <Card style={styles.card}>
      <Text style={styles.title}>Water Reminder</Text>
      <View style={styles.reminder}>
        <View style={styles.waterInfo}>
          <Droplet size={24} color="#0ea5e9" />
          <Text style={styles.waterAmount}>1.75 L</Text>
          <Text style={styles.waterText}>
            Today you drink 10 glass of water
          </Text>
        </View>
        <View style={styles.glasses}>
          {[1, 2, 3, 4, 5, 6].map((glass) => (
            <View
              key={glass}
              style={[styles.glass, { opacity: glass <= 4 ? 1 : 0.3 }]}
            >
              <Droplet size={16} color="#0ea5e9" />
            </View>
          ))}
        </View>
      </View>
    </Card>
  );
}

export function GoalsCard() {
  return (
    <Card style={styles.card}>
      <View style={styles.goalsHeader}>
        <Text style={styles.title}>Today's Goals</Text>
        <Button variant="ghost">See All</Button>
      </View>

      <View style={styles.goals}>
        <GoalItem
          icon={Target}
          title="Daily Calories"
          value="433"
          target="2267"
          unit="kcal"
          progress={19}
        />
        <GoalItem
          icon={Dumbbell}
          title="Exercise"
          value="22"
          target="30"
          unit="min"
          progress={73}
        />
      </View>
    </Card>
  );
}

function GoalItem({
  icon: Icon,
  title,
  value,
  target,
  unit,
  progress,
}: {
  icon: any;
  title: string;
  value: string;
  target: string;
  unit: string;
  progress: number;
}) {
  return (
    <View style={styles.goalItem}>
      <View style={styles.goalIcon}>
        <Icon size={24} />
      </View>
      <View style={styles.goalInfo}>
        <Text style={styles.goalTitle}>{title}</Text>
        <Text style={styles.goalProgress}>
          {value}/{target} {unit}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    padding: 16,
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  reminder: {
    gap: 16,
  },
  waterInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  waterAmount: {
    fontSize: 24,
    fontWeight: '600',
  },
  waterText: {
    fontSize: 14,
    opacity: 0.7,
  },
  glasses: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  glass: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f0f9ff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  goalsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  goals: {
    gap: 16,
  },
  goalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  goalIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f8f8f8',
    alignItems: 'center',
    justifyContent: 'center',
  },
  goalInfo: {
    flex: 1,
    gap: 4,
  },
  goalTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  goalProgress: {
    fontSize: 14,
    opacity: 0.7,
  },
});
