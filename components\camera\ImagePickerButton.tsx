import React from 'react';
import { TouchableOpacity, Text, StyleSheet, Platform } from 'react-native';
import { Upload } from 'lucide-react-native';
import { theme } from '@/constants/theme';

type ImagePickerButtonProps = {
  onImageSelected: (uri: string) => void;
};

export const ImagePickerButton = ({ onImageSelected }: ImagePickerButtonProps) => {
  const handleImageSelection = () => {
    // For the MVP, we'll simulate the image selection with a placeholder image
    // In a real app, we would use the ImagePicker API
    const mockFoodImages = [
      'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg',
      'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg',
      'https://images.pexels.com/photos/958545/pexels-photo-958545.jpeg',
      'https://images.pexels.com/photos/674574/pexels-photo-674574.jpeg',
    ];
    
    const randomImage = mockFoodImages[Math.floor(Math.random() * mockFoodImages.length)];
    onImageSelected(randomImage);
  };

  return (
    <TouchableOpacity style={styles.button} onPress={handleImageSelection}>
      <Upload size={24} color={theme.colors.white} />
      <Text style={styles.buttonText}>Upload Image</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.m,
    paddingHorizontal: theme.spacing.xl,
    borderRadius: theme.borderRadius.m,
    marginTop: theme.spacing.m,
  },
  buttonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    marginLeft: theme.spacing.s,
  },
});