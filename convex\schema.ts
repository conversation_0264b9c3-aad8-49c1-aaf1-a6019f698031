import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
  activeHealthPlan: defineTable({
    userId: v.string(),
    planId: v.id('healthPlan'),
    startDate: v.string(),
    endDate: v.optional(v.string()),
    isDeleted: v.optional(v.boolean()),
  })
    .index('by_user', ['userId'])
    .index('by_dates', ['startDate', 'endDate']),

  healthPlan: defineTable({
    userId: v.string(),
    isActive: v.boolean(),
    dailyCalories: v.object({
      breakfast: v.number(),
      lunch: v.number(),
      dinner: v.number(),
      snacks: v.number(),
    }),
    dailyNutrition: v.optional(
      v.object({
        calories: v.number(),
        protein: v.number(), // grams
        carbs: v.number(), // grams
        fat: v.number(), // grams
        fiber: v.number(), // grams
        sugar: v.number(), // grams
        sodium: v.number(), // mg
        water: v.number(), // ml
        calcium: v.number(), // mg
        iron: v.number(), // mg
        vitaminC: v.number(), // mg
        vitaminD: v.number(), // IU
      }),
    ),
    type: v.union(
      v.literal('weight'),
      v.literal('heart'),
      v.literal('prenatal'),
      v.literal('sports'),
      v.literal('recovery'),
      v.literal('senior'),
      v.literal('gluten'),
      v.literal('diabetes'),
      v.literal('gut'),
      v.literal('vegan'),
      v.literal('muscle'),
      v.literal('skin'),
      v.literal('detox'),
      v.literal('mediterranean'),
      v.literal('keto'),
      v.literal('intermittent'),
      v.literal('dash'),
      v.literal('paleo'),
    ),
    startDate: v.string(),
    endDate: v.optional(v.string()),
  }),

  meal: defineTable({
    userId: v.string(),
    name: v.string(),
    description: v.string(),
    imageUrl: v.optional(v.string()),
    healthPlanId: v.optional(v.id('healthPlan')),
    calories: v.number(),
    protein: v.number(),
    carbs: v.number(),
    fat: v.number(),
    // Extended nutrition fields
    fiber: v.optional(v.number()), // grams
    sugar: v.optional(v.number()), // grams
    sodium: v.optional(v.number()), // mg
    water: v.optional(v.number()), // ml
    calcium: v.optional(v.number()), // mg
    iron: v.optional(v.number()), // mg
    vitaminC: v.optional(v.number()), // mg
    vitaminD: v.optional(v.number()), // IU
    type: v.union(
      v.literal('breakfast'),
      v.literal('lunch'),
      v.literal('dinner'),
      v.literal('snacks'),
    ),
    date: v.string(),
    isDeleted: v.optional(v.boolean()),
  })
    .index('by_user_date', ['userId', 'date'])
    .index('by_meal_type', ['userId', 'type', 'date']),

  drink: defineTable({
    userId: v.string(),
    isDeleted: v.optional(v.boolean()),
    name: v.string(),
    description: v.string(),
    calories: v.number(),
    protein: v.number(),
    carbs: v.number(),
    fat: v.number(),
    // Extended nutrition fields
    fiber: v.optional(v.number()), // grams
    sugar: v.optional(v.number()), // grams
    sodium: v.optional(v.number()), // mg
    water: v.optional(v.number()), // ml
    calcium: v.optional(v.number()), // mg
    iron: v.optional(v.number()), // mg
    vitaminC: v.optional(v.number()), // mg
    vitaminD: v.optional(v.number()), // IU
    type: v.union(
      v.literal('breakfast'),
      v.literal('lunch'),
      v.literal('dinner'),
      v.literal('snacks'),
    ),
    date: v.string(),
  }),

  aiMealAdvices: defineTable({
    userId: v.string(),
    advices: v.array(
      v.object({
        title: v.string(),
        description: v.string(),
        seriousness: v.union(
          v.literal('low'),
          v.literal('medium'),
          v.literal('high'),
        ),
      }),
    ),
    date: v.string(),
  }),

  activity: defineTable({
    userId: v.string(),
    description: v.string(),
    date: v.string(),
  }),

  exercisePlan: defineTable({
    userId: v.string(),
    name: v.string(),
    description: v.string(),
    startDate: v.string(),
    endDate: v.string(),
    targetDaysPerWeek: v.number(),
    exercises: v.array(
      v.object({
        name: v.string(),
        sets: v.number(),
        reps: v.number(),
        weight: v.optional(v.number()),
        duration: v.optional(v.number()),
        restTime: v.optional(v.number()),
      }),
    ),
    healthPlanId: v.optional(v.id('healthPlan')),
    isActive: v.boolean(),
  })
    .index('by_user', ['userId'])
    .index('by_date_range', ['startDate', 'endDate']),

  exerciseSession: defineTable({
    userId: v.string(),
    planId: v.id('exercisePlan'),
    date: v.string(),
    completed: v.boolean(),
    exercises: v.array(
      v.object({
        name: v.string(),
        sets: v.array(
          v.object({
            reps: v.number(),
            weight: v.optional(v.number()),
            duration: v.optional(v.number()),
          }),
        ),
        notes: v.optional(v.string()),
      }),
    ),
  })
    .index('by_user_date', ['userId', 'date'])
    .index('by_plan', ['planId']),

  workout: defineTable({
    userId: v.string(),
    name: v.string(),
    type: v.string(), // 'Strength', 'Cardio', 'Flexibility', 'HIIT', 'Mixed'
    description: v.optional(v.string()),
    scheduledDate: v.string(), // ISO string
    estimatedDuration: v.number(), // in minutes
    exercises: v.array(
      v.object({
        name: v.string(),
        sets: v.number(),
        reps: v.number(),
        weight: v.optional(v.number()),
        duration: v.optional(v.number()),
        restTime: v.optional(v.number()),
      }),
    ),
    completed: v.boolean(),
    completedAt: v.optional(v.string()),
  })
    .index('by_user', ['userId'])
    .index('by_user_date', ['userId', 'scheduledDate'])
    .index('by_user_completed', ['userId', 'completed']),

  workoutNotification: defineTable({
    userId: v.string(),
    workoutId: v.id('workout'),
    scheduledTime: v.string(), // ISO string
    sent: v.boolean(),
    type: v.union(v.literal('reminder'), v.literal('motivation')),
  })
    .index('by_user', ['userId'])
    .index('by_scheduled_time', ['scheduledTime'])
    .index('by_workout', ['workoutId']),

  sleep: defineTable({
    userId: v.string(),
    date: v.string(), // ISO date string for the day the sleep ended
    startTime: v.string(), // ISO datetime string
    endTime: v.string(), // ISO datetime string
    durationHours: v.number(),
    qualityRating: v.number(), // e.g., 1-5
    notes: v.optional(v.string()),
  }).index('by_user_date', ['userId', 'date']),

  // Meditation Sessions
  meditationSession: defineTable({
    userId: v.string(),
    date: v.string(), // ISO date string
    type: v.string(), // e.g., 'breathing', 'mindfulness', 'body-scan', 'nature'
    durationMinutes: v.number(),
    completed: v.boolean(),
    notes: v.optional(v.string()),
  }).index('by_user_date', ['userId', 'date']),

  // Wellbeing Insights
  wellbeingInsight: defineTable({
    userId: v.string(),
    date: v.string(), // ISO date string
    category: v.string(), // e.g., 'sleep', 'mood', 'meditation', 'general'
    title: v.string(),
    description: v.string(),
    recommendation: v.string(),
    priority: v.union(v.literal('high'), v.literal('medium'), v.literal('low')),
    viewed: v.boolean(),
  }).index('by_user_date', ['userId', 'date']),

  // --- Mental Health Tracking ---
  moodEntry: defineTable({
    userId: v.string(),
    date: v.string(), // ISO date string
    mood: v.union(
      v.literal('happy'),
      v.literal('sad'),
      v.literal('anxious'),
      v.literal('angry'),
      v.literal('neutral'),
      v.literal('excited'),
      v.literal('tired'),
      v.literal('stressed'),
      v.literal('calm'),
      v.literal('other'),
    ),
  }).index('by_user_date', ['userId', 'date']),

  wellbeingEntry: defineTable({
    userId: v.string(),
    date: v.string(), // ISO date string
    stress: v.optional(v.number()), // 1-10
    anxiety: v.optional(v.number()), // 1-10
    happiness: v.optional(v.number()), // 1-10
    energy: v.optional(v.number()), // 1-10
    notes: v.optional(v.string()),
  }).index('by_user_date', ['userId', 'date']),

  mentalHealthActivity: defineTable({
    userId: v.string(),
    date: v.string(), // ISO date string
    type: v.string(), // e.g., 'journal', 'gratitude', 'affirmation', etc.
    content: v.optional(v.string()),
  }).index('by_user_date', ['userId', 'date']),

  userProfile: defineTable({
    userId: v.string(),
    dateOfBirth: v.optional(v.string()), // ISO date string
    weight: v.optional(v.number()), // in kg
    height: v.optional(v.number()), // in cm
    gender: v.optional(
      v.union(v.literal('male'), v.literal('female'), v.literal('other')),
    ),
    activityLevel: v.optional(
      v.union(
        v.literal('sedentary'),
        v.literal('lightly_active'),
        v.literal('moderately_active'),
        v.literal('very_active'),
        v.literal('extra_active'),
      ),
    ),
    targetWeight: v.optional(v.number()),
    targetCalories: v.optional(v.number()),
    hasCompletedOnboarding: v.optional(v.boolean()),
  }).index('by_user', ['userId']),
});
