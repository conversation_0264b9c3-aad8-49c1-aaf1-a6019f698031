import React from 'react';
import {
  View,
  ViewStyle,
  Animated,
  Easing,
  DimensionValue,
  StyleSheet,
} from 'react-native';

interface SkeletonProps {
  width?: DimensionValue;
  height?: DimensionValue;
  borderRadius?: number;
  style?: ViewStyle;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
}) => {
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const animation = Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      }),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, []);

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['-100%', '100%'],
  });

  return (
    <View
      style={[
        {
          width,
          height,
          borderRadius,
        },
        style,
      ]}
    >
      <Animated.View
        style={{
          width: '100%',
          height: '100%',
          transform: [{ translateX }],
        }}
      />
    </View>
  );
};

// Example usage components
export const SkeletonText: React.FC<{ width?: DimensionValue }> = ({
  width,
}) => <Skeleton width={width} height={16} borderRadius={4} />;

export const SkeletonCircle: React.FC<{ size: number }> = ({ size }) => (
  <Skeleton width={size} height={size} borderRadius={size / 2} />
);

export const SkeletonImage: React.FC<{
  width?: DimensionValue;
  height?: DimensionValue;
}> = ({ width = '100%', height = 200 }) => (
  <Skeleton width={width} height={height} borderRadius={8} />
);

interface SkeletonLayoutProps {
  style?: ViewStyle;
  showHeader?: boolean;
  showGrid?: boolean;
  showFooter?: boolean;
  gridItems?: number;
}

export const SkeletonLayout: React.FC<SkeletonLayoutProps> = ({
  style,
  showHeader = true,
  showGrid = true,
  showFooter = true,
  gridItems = 4,
}) => {
  return (
    <View style={[styles.container, style]}>
      {/* Header Section */}
      {showHeader && (
        <View style={styles.header}>
          <SkeletonCircle size={60} />
          <View style={styles.headerText}>
            <SkeletonText width="60%" />
            <SkeletonText width="40%" />
          </View>
        </View>
      )}

      {/* Content Lines */}
      <View style={styles.content}>
        <SkeletonText width="90%" />
        <SkeletonText width="85%" />
        <SkeletonText width="75%" />
      </View>

      {/* Grid Section */}
      {showGrid && (
        <View style={styles.grid}>
          {Array.from({ length: gridItems }).map((_, index) => (
            <View key={index} style={styles.gridItem}>
              <SkeletonImage height={120} />
              <SkeletonText width="80%" />
              <SkeletonText width="60%" />
            </View>
          ))}
        </View>
      )}

      {/* Footer Section */}
      {showFooter && (
        <View style={styles.footer}>
          <View style={styles.footerItem}>
            <SkeletonCircle size={24} />
            <SkeletonText width="60%" />
          </View>
          <View style={styles.footerItem}>
            <SkeletonCircle size={24} />
            <SkeletonText width="60%" />
          </View>
          <View style={styles.footerItem}>
            <SkeletonCircle size={24} />
            <SkeletonText width="60%" />
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  headerText: {
    marginLeft: 16,
    flex: 1,
    gap: 8,
  },
  content: {
    gap: 12,
    marginBottom: 24,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 24,
  },
  gridItem: {
    width: '47%',
    gap: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
  },
  footerItem: {
    alignItems: 'center',
    gap: 8,
  },
});
