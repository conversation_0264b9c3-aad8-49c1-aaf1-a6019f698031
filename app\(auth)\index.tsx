import React, { useCallback, useEffect, useState } from 'react';
import { View, Image, Linking } from 'react-native';
import { useAuth, useSSO } from '@clerk/clerk-expo';
import * as AuthSession from 'expo-auth-session';
import { Redirect } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import { Apple } from 'lucide-react-native';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';

export const useWarmUpBrowser = () => {
  useEffect(() => {
    void WebBrowser.warmUpAsync();
    return () => {
      void WebBrowser.coolDownAsync();
    };
  }, []);
};

export default function SignInScreen() {
  useWarmUpBrowser();
  const { isLoaded, sessionId } = useAuth();
  const { startSSOFlow } = useSSO();

  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');

  const onGooglePress = useCallback(async () => {
    try {
      setLoading(true);
      const { createdSessionId, setActive } = await startSSOFlow({
        strategy: 'oauth_google',
        redirectUrl: AuthSession.makeRedirectUri(),
      });

      if (createdSessionId) {
        setActive!({ session: createdSessionId });
      }
    } catch (err) {
      console.log(JSON.stringify(err, null, 2));
    } finally {
      setLoading(false);
    }
  }, []);

  const onApplePress = useCallback(async () => {
    try {
      setLoading(true);
      const { createdSessionId, setActive } = await startSSOFlow({
        strategy: 'oauth_apple',
        redirectUrl: AuthSession.makeRedirectUri(),
      });

      if (createdSessionId) {
        setActive!({ session: createdSessionId });
      }
    } catch (err) {
      console.log(JSON.stringify(err, null, 2));
    } finally {
      setLoading(false);
    }
  }, []);

  if (!isLoaded) return null;

  if (sessionId) {
    return <Redirect href="/(tabs)" />;
  }

  const openTerms = () => Linking.openURL('https://example.com/terms');
  const openPrivacy = () => Linking.openURL('https://example.com/privacy');

  return (
    <View className="flex-1" style={{ flex: 1 }}>
      <View className="flex-1 px-6 py-16 bg-[#cfc7f6] rounded-b-3xl justify-center items-center">
        <View className="w-[400px] h-[400px] rounded-full justify-center items-center mb-6">
          <Image
            source={require('../../assets/images/auth.jpg')}
            className="w-[400px] h-[400px] rounded-full"
          />
        </View>

        <View className="items-center px-6">
          <Text className="font-black text-4xl text-center mb-2">
            Welcome to Ray AI {sessionId ? 'again' : sessionId + 'new'}
          </Text>
          <Text className="font-normal text-muted-foreground text-base text-center">
            Manage your health, fitness, calories and overall wellbeing.
          </Text>
        </View>
      </View>

      {/* Auth Section */}
      <View className="px-6 pb-12 pt-6">
        <View className="w-full space-y-4">
          <Button
            disabled={loading}
            className="flex flex-row items-center mt-4"
            onPress={onApplePress}
            variant={'outline'}
          >
            <Apple size={18} color="black" />
            <Text className="ml-3 font-medium">Continue with Apple</Text>
          </Button>

          <Button
            disabled={loading}
            onPress={onGooglePress}
            className="flex flex-row items-center mt-4"
            variant={'outline'}
          >
            <Image
              source={{ uri: 'https://www.google.com/favicon.ico' }}
              className="w-5 h-5"
            />
            <Text className="ml-3 font-medium">Continue with Google</Text>
          </Button>

          {/* Terms and Privacy */}
          <Text className="text-gray-500 mb-10 text-xs mt-6 text-center px-4 leading-5">
            By continuing, you agree to our{' '}
            <Text
              className="text-primary underline text-xs"
              onPress={openTerms}
            >
              Terms of Service
            </Text>{' '}
            and{' '}
            <Text
              className="text-primary underline text-xs"
              onPress={openPrivacy}
            >
              Privacy Policy
            </Text>
          </Text>
        </View>
      </View>
    </View>
  );
}
