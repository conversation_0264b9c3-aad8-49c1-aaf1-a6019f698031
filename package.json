{"name": "mhh", "main": "index.js", "version": "1.0.0", "scripts": {"dev": "expo start -c", "dev:web": "expo start -c --web", "dev:android": "expo start -c --android", "android": "expo start -c --android", "ios": "expo start -c --ios", "web": "expo start -c --web", "clean": "rm -rf .expo node_modules", "postinstall": "npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@clerk/clerk-expo": "^2.13.0", "@expo-google-fonts/poppins": "^0.4.0", "@gorhom/bottom-sheet": "^5.1.6", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.1", "@react-native-community/slider": "^4.5.7", "@react-navigation/native": "^7.0.0", "@rn-primitives/accordion": "^1.2.0", "@rn-primitives/alert-dialog": "^1.2.0", "@rn-primitives/aspect-ratio": "^1.2.0", "@rn-primitives/avatar": "~1.2.0", "@rn-primitives/checkbox": "^1.2.0", "@rn-primitives/collapsible": "^1.2.0", "@rn-primitives/context-menu": "^1.2.0", "@rn-primitives/dialog": "^1.2.0", "@rn-primitives/dropdown-menu": "^1.2.0", "@rn-primitives/hover-card": "^1.2.0", "@rn-primitives/label": "^1.2.0", "@rn-primitives/menubar": "^1.2.0", "@rn-primitives/navigation-menu": "^1.2.0", "@rn-primitives/popover": "^1.2.0", "@rn-primitives/portal": "~1.3.0", "@rn-primitives/progress": "~1.2.0", "@rn-primitives/radio-group": "^1.2.0", "@rn-primitives/select": "^1.2.0", "@rn-primitives/separator": "^1.2.0", "@rn-primitives/slot": "~1.2.0", "@rn-primitives/switch": "^1.2.0", "@rn-primitives/table": "^1.2.0", "@rn-primitives/tabs": "^1.2.0", "@rn-primitives/toggle": "^1.2.0", "@rn-primitives/toggle-group": "^1.2.0", "@rn-primitives/tooltip": "~1.2.0", "@shopify/flash-list": "^1.8.3", "@types/date-fns": "^2.5.3", "ai": "^4.3.16", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "convex": "^1.24.8", "date-fns": "^4.1.0", "expo": "^53.0.9", "expo-auth-session": "^6.2.0", "expo-av": "^15.1.5", "expo-background-fetch": "^13.1.5", "expo-blur": "^14.1.5", "expo-camera": "^16.1.7", "expo-constants": "^17.1.6", "expo-dev-client": "~5.2.1", "expo-font": "^13.3.1", "expo-haptics": "^14.1.4", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-navigation-bar": "~4.2.4", "expo-notifications": "^0.31.3", "expo-router": "~5.0.7", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-web-browser": "^14.1.6", "lottie-react-native": "^7.2.2", "lucide-react-native": "^0.511.0", "moment": "^2.30.1", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-charts": "^1.4.61", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "tailwind-merge": "^2.2.1", "tailwindcss": "3.3.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.56"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~19.0.14", "lightningcss": "^1.30.1", "typescript": "^5.8.3"}, "private": true}