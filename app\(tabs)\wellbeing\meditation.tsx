import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import {
  Brain,
  Play,
  Pause,
  RotateCcw,
  ChevronLeft,
  Clock,
  Heart,
  Leaf,
  Wind,
  Mountain,
  Waves,
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

const meditationTypes = [
  {
    id: 'breathing',
    title: 'Breathing Exercise',
    description: 'Simple breathing techniques to calm your mind',
    duration: 5,
    icon: Wind,
    color: '#06b6d4',
    gradient: ['#06b6d4', '#0891b2'],
  },
  {
    id: 'mindfulness',
    title: 'Mindfulness',
    description: 'Present moment awareness meditation',
    duration: 10,
    icon: Leaf,
    color: '#22c55e',
    gradient: ['#22c55e', '#16a34a'],
  },
  {
    id: 'body-scan',
    title: 'Body Scan',
    description: 'Progressive relaxation through body awareness',
    duration: 15,
    icon: Heart,
    color: '#f59e0b',
    gradient: ['#f59e0b', '#d97706'],
  },
  {
    id: 'nature',
    title: 'Nature Sounds',
    description: 'Meditate with calming nature sounds',
    duration: 20,
    icon: Mountain,
    color: '#8b5cf6',
    gradient: ['#8b5cf6', '#7c3aed'],
  },
];

export default function Meditation() {
  const router = useRouter();
  const [selectedMeditation, setSelectedMeditation] = useState<any>(null);
  const [isActive, setIsActive] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const [totalTime, setTotalTime] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((time) => {
          if (time <= 1) {
            setIsActive(false);
            Alert.alert('Session Complete', 'Great job! Your meditation session is complete.');
            return 0;
          }
          return time - 1;
        });
      }, 1000);
    } else if (!isActive && timeLeft !== 0) {
      if (interval) clearInterval(interval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, timeLeft]);

  const startMeditation = (meditation: any) => {
    setSelectedMeditation(meditation);
    setTimeLeft(meditation.duration * 60); // Convert minutes to seconds
    setTotalTime(meditation.duration * 60);
    setIsActive(true);
  };

  const toggleTimer = () => {
    setIsActive(!isActive);
  };

  const resetTimer = () => {
    setIsActive(false);
    setTimeLeft(totalTime);
  };

  const stopMeditation = () => {
    setIsActive(false);
    setSelectedMeditation(null);
    setTimeLeft(0);
    setTotalTime(0);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgress = () => {
    if (totalTime === 0) return 0;
    return ((totalTime - timeLeft) / totalTime) * 100;
  };

  if (selectedMeditation) {
    return (
      <SafeAreaView className="flex-1">
        <LinearGradient
          colors={selectedMeditation.gradient}
          className="flex-1"
        >
          <View className="flex-row items-center px-6 py-4">
            <TouchableOpacity onPress={stopMeditation} className="mr-4">
              <ChevronLeft size={24} color="white" />
            </TouchableOpacity>
            <Text className="text-xl font-semibold text-white flex-1">
              {selectedMeditation.title}
            </Text>
          </View>

          <View className="flex-1 items-center justify-center px-6">
            {/* Timer Circle */}
            <View className="relative items-center justify-center mb-8">
              <View className="w-64 h-64 rounded-full border-4 border-white/30 items-center justify-center">
                <View className="w-56 h-56 rounded-full border-2 border-white/50 items-center justify-center">
                  <Text className="text-6xl font-light text-white mb-2">
                    {formatTime(timeLeft)}
                  </Text>
                  <Text className="text-white/80 text-lg">
                    {Math.ceil(timeLeft / 60)} min remaining
                  </Text>
                </View>
              </View>
              
              {/* Progress indicator */}
              <View 
                className="absolute top-0 left-0 w-64 h-64 rounded-full border-4 border-transparent"
                style={{
                  borderTopColor: 'white',
                  transform: [{ rotate: `${(getProgress() * 3.6) - 90}deg` }],
                }}
              />
            </View>

            {/* Meditation Icon */}
            <View className="w-20 h-20 rounded-full bg-white/20 items-center justify-center mb-8">
              <selectedMeditation.icon size={40} color="white" />
            </View>

            {/* Instructions */}
            <Text className="text-white text-center text-lg mb-8 px-4 leading-relaxed">
              {selectedMeditation.id === 'breathing' && 
                "Focus on your breath. Inhale slowly for 4 counts, hold for 4, then exhale for 4."
              }
              {selectedMeditation.id === 'mindfulness' && 
                "Notice your thoughts without judgment. Let them pass like clouds in the sky."
              }
              {selectedMeditation.id === 'body-scan' && 
                "Start from your toes and slowly move your attention up through your body."
              }
              {selectedMeditation.id === 'nature' && 
                "Imagine yourself in a peaceful natural setting. Listen to the sounds around you."
              }
            </Text>

            {/* Controls */}
            <View className="flex-row items-center space-x-6">
              <TouchableOpacity
                onPress={resetTimer}
                className="w-16 h-16 rounded-full bg-white/20 items-center justify-center"
              >
                <RotateCcw size={24} color="white" />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={toggleTimer}
                className="w-20 h-20 rounded-full bg-white items-center justify-center"
              >
                {isActive ? (
                  <Pause size={32} color={selectedMeditation.color} />
                ) : (
                  <Play size={32} color={selectedMeditation.color} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                onPress={stopMeditation}
                className="w-16 h-16 rounded-full bg-white/20 items-center justify-center"
              >
                <ChevronLeft size={24} color="white" />
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <View className="flex-row items-center px-6 py-4 bg-white border-b border-gray-200">
        <TouchableOpacity onPress={() => router.back()} className="mr-4">
          <ChevronLeft size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-xl font-semibold text-gray-900">Meditation</Text>
      </View>

      <ScrollView className="flex-1 px-6 py-6">
        {/* Header */}
        <View className="mb-6">
          <Text className="text-2xl font-bold text-gray-900 mb-2">
            Find Your Peace
          </Text>
          <Text className="text-gray-600">
            Choose a meditation practice that resonates with you today
          </Text>
        </View>

        {/* Meditation Options */}
        <View className="space-y-4 mb-6">
          {meditationTypes.map((meditation) => (
            <TouchableOpacity
              key={meditation.id}
              onPress={() => startMeditation(meditation)}
              className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden"
            >
              <LinearGradient
                colors={[`${meditation.color}20`, `${meditation.color}10`]}
                className="p-6"
              >
                <View className="flex-row items-center justify-between">
                  <View className="flex-1">
                    <View className="flex-row items-center mb-2">
                      <View 
                        className="w-10 h-10 rounded-xl items-center justify-center mr-3"
                        style={{ backgroundColor: `${meditation.color}30` }}
                      >
                        <meditation.icon size={20} color={meditation.color} />
                      </View>
                      <View className="flex-1">
                        <Text className="text-lg font-semibold text-gray-900">
                          {meditation.title}
                        </Text>
                        <View className="flex-row items-center">
                          <Clock size={14} color="#6b7280" />
                          <Text className="text-gray-600 text-sm ml-1">
                            {meditation.duration} min
                          </Text>
                        </View>
                      </View>
                    </View>
                    <Text className="text-gray-600 text-sm">
                      {meditation.description}
                    </Text>
                  </View>
                  <View 
                    className="w-12 h-12 rounded-full items-center justify-center ml-4"
                    style={{ backgroundColor: meditation.color }}
                  >
                    <Play size={20} color="white" />
                  </View>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          ))}
        </View>

        {/* Benefits Section */}
        <View className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <Text className="text-xl font-semibold text-gray-900 mb-4">
            Benefits of Meditation
          </Text>
          
          <View className="space-y-3">
            <View className="flex-row items-start">
              <View className="w-2 h-2 rounded-full bg-cyan-500 mt-2 mr-3" />
              <Text className="text-gray-700 flex-1">
                Reduces stress and anxiety levels
              </Text>
            </View>
            <View className="flex-row items-start">
              <View className="w-2 h-2 rounded-full bg-green-500 mt-2 mr-3" />
              <Text className="text-gray-700 flex-1">
                Improves focus and concentration
              </Text>
            </View>
            <View className="flex-row items-start">
              <View className="w-2 h-2 rounded-full bg-orange-500 mt-2 mr-3" />
              <Text className="text-gray-700 flex-1">
                Enhances emotional well-being
              </Text>
            </View>
            <View className="flex-row items-start">
              <View className="w-2 h-2 rounded-full bg-purple-500 mt-2 mr-3" />
              <Text className="text-gray-700 flex-1">
                Promotes better sleep quality
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}