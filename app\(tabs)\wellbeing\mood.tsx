import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import {
  Smile,
  Frown,
  Meh,
  Heart,
  Zap,
  Cloud,
  Moon,
  Leaf,
  ChevronLeft,
  Plus,
  Calendar,
  TrendingUp,
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { <PERSON><PERSON><PERSON>, PieChart } from 'react-native-gifted-charts';

const { width } = Dimensions.get('window');

const moodOptions = [
  { mood: 'happy', icon: Smile, color: '#22c55e', label: 'Happy' },
  { mood: 'excited', icon: Zap, color: '#f59e0b', label: 'Excited' },
  { mood: 'calm', icon: Leaf, color: '#06b6d4', label: 'Calm' },
  { mood: 'neutral', icon: Meh, color: '#6b7280', label: 'Neutral' },
  { mood: 'tired', icon: Moon, color: '#8b5cf6', label: 'Tired' },
  { mood: 'stressed', icon: Cloud, color: '#ef4444', label: 'Stressed' },
  { mood: 'anxious', icon: Cloud, color: '#f97316', label: 'Anxious' },
  { mood: 'sad', icon: Frown, color: '#3b82f6', label: 'Sad' },
];

export default function MoodTracking() {
  const router = useRouter();
  const [showForm, setShowForm] = useState(false);
  const [selectedMood, setSelectedMood] = useState<string | null>(null);
  const [intensity, setIntensity] = useState(5);
  const [note, setNote] = useState('');

  const createMoodEntry = useMutation(api.mood.createMoodEntry);
  const recentMoodEntries = useQuery(api.mood.getMoodEntries, {
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });
  const moodSummary = useQuery(api.mood.getMoodSummary, {
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });
  const currentMood = useQuery(api.mood.getCurrentMood);

  const handleSaveMood = async () => {
    if (!selectedMood) {
      Alert.alert('Error', 'Please select a mood');
      return;
    }

    try {
      await createMoodEntry({
        date: new Date().toISOString().split('T')[0],
        mood: selectedMood as any,
        intensity,
        note: note || undefined,
      });

      Alert.alert('Success', 'Mood entry saved successfully!');
      setShowForm(false);
      setSelectedMood(null);
      setIntensity(5);
      setNote('');
    } catch (error) {
      Alert.alert('Error', 'Failed to save mood entry');
    }
  };

  const getMoodIcon = (mood: string) => {
    const moodOption = moodOptions.find(option => option.mood === mood);
    return moodOption ? moodOption.icon : Meh;
  };

  const getMoodColor = (mood: string) => {
    const moodOption = moodOptions.find(option => option.mood === mood);
    return moodOption ? moodOption.color : '#6b7280';
  };

  const getMoodLabel = (mood: string) => {
    const moodOption = moodOptions.find(option => option.mood === mood);
    return moodOption ? moodOption.label : mood;
  };

  // Prepare pie chart data
  const pieData = moodSummary?.moodCounts ? Object.entries(moodSummary.moodCounts).map(([mood, count]) => ({
    value: count,
    color: getMoodColor(mood),
    text: `${count}`,
    label: getMoodLabel(mood),
  })) : [];

  if (showForm) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50">
        <View className="flex-row items-center px-6 py-4 bg-white border-b border-gray-200">
          <TouchableOpacity onPress={() => setShowForm(false)} className="mr-4">
            <ChevronLeft size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="text-xl font-semibold text-gray-900">Log Mood</Text>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          {/* Mood Selection */}
          <View className="bg-white rounded-2xl p-4 mb-4 shadow-sm border border-gray-100">
            <Text className="text-lg font-semibold text-gray-900 mb-4 text-center">
              How are you feeling?
            </Text>
            
            <View className="flex-row flex-wrap justify-center">
              {moodOptions.map(({ mood, icon: Icon, color, label }) => (
                <TouchableOpacity
                  key={mood}
                  onPress={() => setSelectedMood(mood)}
                  className={`m-2 p-4 rounded-2xl items-center ${
                    selectedMood === mood ? 'border-2' : 'border border-gray-200'
                  }`}
                  style={{
                    backgroundColor: selectedMood === mood ? `${color}20` : 'white',
                    borderColor: selectedMood === mood ? color : '#e5e7eb',
                    width: (width - 80) / 4 - 16,
                  }}
                >
                  <Icon size={32} color={color} />
                  <Text className="text-xs mt-2 text-center font-medium" style={{ color }}>
                    {label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Intensity Slider */}
          <View className="bg-white rounded-2xl p-4 mb-4 shadow-sm border border-gray-100">
            <Text className="text-lg font-semibold text-gray-900 mb-4 text-center">
              Intensity Level
            </Text>
            
            <View className="items-center">
              <Text className="text-3xl font-bold mb-4" style={{ color: selectedMood ? getMoodColor(selectedMood) : '#6b7280' }}>
                {intensity}/10
              </Text>
              
              <View className="flex-row items-center justify-between w-full mb-4">
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((level) => (
                  <TouchableOpacity
                    key={level}
                    onPress={() => setIntensity(level)}
                    className={`w-8 h-8 rounded-full items-center justify-center ${
                      level <= intensity ? 'opacity-100' : 'opacity-30'
                    }`}
                    style={{
                      backgroundColor: selectedMood ? getMoodColor(selectedMood) : '#6b7280',
                    }}
                  >
                    <Text className="text-white text-xs font-bold">{level}</Text>
                  </TouchableOpacity>
                ))}
              </View>
              
              <View className="flex-row justify-between w-full">
                <Text className="text-gray-500 text-sm">Low</Text>
                <Text className="text-gray-500 text-sm">High</Text>
              </View>
            </View>
          </View>

          {/* Notes */}
          <View className="bg-white rounded-2xl p-4 mb-6 shadow-sm border border-gray-100">
            <Text className="text-lg font-semibold text-gray-900 mb-3">Notes (Optional)</Text>
            <TextInput
              value={note}
              onChangeText={setNote}
              placeholder="What's on your mind? Any specific reasons for this mood?"
              multiline
              numberOfLines={4}
              className="bg-gray-50 rounded-xl p-3 text-gray-900"
              textAlignVertical="top"
            />
          </View>

          {/* Save Button */}
          <TouchableOpacity
            onPress={handleSaveMood}
            disabled={!selectedMood}
            className={`rounded-2xl p-4 items-center ${
              selectedMood ? 'opacity-100' : 'opacity-50'
            }`}
            style={{
              backgroundColor: selectedMood ? getMoodColor(selectedMood) : '#6b7280',
            }}
          >
            <Text className="text-white font-semibold text-lg">Save Mood Entry</Text>
          </TouchableOpacity>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <View className="flex-row items-center justify-between px-6 py-4 bg-white border-b border-gray-200">
        <TouchableOpacity onPress={() => router.back()} className="mr-4">
          <ChevronLeft size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-xl font-semibold text-gray-900 flex-1">Mood Tracking</Text>
        <TouchableOpacity
          onPress={() => setShowForm(true)}
          className="bg-orange-500 rounded-full p-2"
        >
          <Plus size={20} color="white" />
        </TouchableOpacity>
      </View>

      <ScrollView className="flex-1 px-6 py-6">
        {/* Current Mood */}
        {currentMood && (
          <View className="mb-6">
            <LinearGradient
              colors={[getMoodColor(currentMood.mood), `${getMoodColor(currentMood.mood)}80`]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              className="rounded-3xl p-6"
            >
              <View className="flex-row items-center justify-between">
                <View className="flex-1">
                  <Text className="text-white text-lg font-semibold mb-2">
                    Current Mood
                  </Text>
                  <Text className="text-white text-3xl font-bold mb-1">
                    {getMoodLabel(currentMood.mood)}
                  </Text>
                  <Text className="text-white/80 text-sm">
                    Intensity: {currentMood.intensity}/10
                  </Text>
                </View>
                <View className="w-16 h-16 rounded-full bg-white/20 items-center justify-center">
                  {React.createElement(getMoodIcon(currentMood.mood), { size: 32, color: 'white' })}
                </View>
              </View>
            </LinearGradient>
          </View>
        )}

        {/* Mood Summary */}
        <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-100">
          <Text className="text-xl font-semibold text-gray-900 mb-4">This Month</Text>
          
          <View className="flex-row justify-between items-center mb-4">
            <View className="items-center flex-1">
              <Text className="text-3xl font-bold text-orange-500">
                {moodSummary?.totalEntries || 0}
              </Text>
              <Text className="text-gray-600 text-sm">Entries</Text>
            </View>
            <View className="items-center flex-1">
              <Text className="text-3xl font-bold text-blue-600">
                {moodSummary?.averageIntensity?.toFixed(1) || '0'}
              </Text>
              <Text className="text-gray-600 text-sm">Avg Intensity</Text>
            </View>
            <View className="items-center flex-1">
              <Text className="text-3xl font-bold text-green-600">
                {recentMoodEntries?.length || 0}
              </Text>
              <Text className="text-gray-600 text-sm">This Week</Text>
            </View>
          </View>
        </View>

        {/* Mood Distribution */}
        {pieData.length > 0 && (
          <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-100">
            <Text className="text-xl font-semibold text-gray-900 mb-4">Mood Distribution</Text>
            
            <View className="items-center">
              <PieChart
                data={pieData}
                radius={80}
                innerRadius={40}
                centerLabelComponent={() => (
                  <View className="items-center">
                    <Text className="text-lg font-bold text-gray-900">
                      {moodSummary?.totalEntries}
                    </Text>
                    <Text className="text-xs text-gray-600">entries</Text>
                  </View>
                )}
              />
            </View>

            <View className="flex-row flex-wrap justify-center mt-4">
              {pieData.map((item, index) => (
                <View key={index} className="flex-row items-center m-1">
                  <View
                    className="w-3 h-3 rounded-full mr-2"
                    style={{ backgroundColor: item.color }}
                  />
                  <Text className="text-sm text-gray-600">{item.label}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Mood Trends */}
        {recentMoodEntries && recentMoodEntries.length > 0 && (
          <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-100">
            <Text className="text-xl font-semibold text-gray-900 mb-4">Weekly Trends</Text>
            
            <LineChart
              data={recentMoodEntries.slice(-7).map((entry, index) => ({
                value: entry.intensity || 5,
                label: new Date(entry.date).toLocaleDateString('en-US', { weekday: 'short' }),
                dataPointColor: getMoodColor(entry.mood),
              }))}
              width={width - 80}
              height={160}
              color="#f59e0b"
              thickness={3}
              dataPointsRadius={6}
              hideRules
              xAxisThickness={0}
              yAxisThickness={0}
              yAxisTextStyle={{ color: '#9ca3af', fontSize: 12 }}
              xAxisLabelTextStyle={{ color: '#9ca3af', fontSize: 12 }}
              curved
              maxValue={10}
              noOfSections={5}
            />
          </View>
        )}

        {/* Quick Actions */}
        <View className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <Text className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</Text>
          
          <TouchableOpacity
            onPress={() => setShowForm(true)}
            className="bg-orange-500 rounded-xl p-4 flex-row items-center justify-center mb-3"
          >
            <Plus size={20} color="white" />
            <Text className="text-white font-semibold ml-2">Log Current Mood</Text>
          </TouchableOpacity>

          <TouchableOpacity className="bg-blue-50 rounded-xl p-4 flex-row items-center justify-center">
            <TrendingUp size={20} color="#2563eb" />
            <Text className="text-blue-600 font-semibold ml-2">View Mood Insights</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}