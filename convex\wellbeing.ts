import { mutation, query } from './_generated/server';
import { v } from 'convex/values';
import { Id } from './_generated/dataModel';

// Mutations
export const createWellbeingInsight = mutation({
  args: {
    date: v.string(),
    category: v.string(),
    title: v.string(),
    description: v.string(),
    recommendation: v.string(),
    priority: v.union(v.literal('high'), v.literal('medium'), v.literal('low')),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const insightId = await ctx.db.insert('wellbeingInsight', {
      userId,
      ...args,
      viewed: false,
    });
    return insightId;
  },
});

export const markInsightAsViewed = mutation({
  args: { id: v.id('wellbeingInsight') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const insight = await ctx.db.get(args.id);
    if (!insight) {
      throw new Error('Insight not found');
    }

    if (insight.userId !== userId) {
      throw new Error('Unauthorized');
    }

    await ctx.db.patch(args.id, { viewed: true });
    return { success: true };
  },
});

export const deleteInsight = mutation({
  args: { id: v.id('wellbeingInsight') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const insight = await ctx.db.get(args.id);
    if (!insight) {
      throw new Error('Insight not found');
    }

    if (insight.userId !== userId) {
      throw new Error('Unauthorized');
    }

    await ctx.db.delete(args.id);
    return { success: true };
  },
});

// Queries
export const getWellbeingInsights = query({
  args: {
    startDate: v.optional(v.string()),
    endDate: v.optional(v.string()),
    category: v.optional(v.string()),
    viewed: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    let query = ctx.db
      .query('wellbeingInsight')
      .filter((q) => q.eq(q.field('userId'), userId));

    if (args.startDate) {
      query = query.filter((q) => q.gte(q.field('date'), args.startDate!));
    }

    if (args.endDate) {
      query = query.filter((q) => q.lte(q.field('date'), args.endDate!));
    }

    if (args.category) {
      query = query.filter((q) => q.eq(q.field('category'), args.category));
    }

    if (args.viewed !== undefined) {
      query = query.filter((q) => q.eq(q.field('viewed'), args.viewed));
    }

    return await query.order('desc').collect();
  },
});

export const getWellbeingInsightById = query({
  args: { id: v.id('wellbeingInsight') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    const insight = await ctx.db.get(args.id);
    if (!insight) {
      throw new Error('Insight not found');
    }

    if (insight.userId !== userId) {
      throw new Error('Unauthorized');
    }

    return insight;
  },
});

export const getWellbeingScore = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const userId = identity.subject;

    // Get recent sleep data
    const sleepEntries = await ctx.db
      .query('sleep')
      .filter((q) => q.eq(q.field('userId'), userId))
      .order('desc')
      .take(7);

    // Get recent mood data
    const moodEntries = await ctx.db
      .query('moodEntry')
      .filter((q) => q.eq(q.field('userId'), userId))
      .order('desc')
      .take(7);

    // Get meditation sessions
    const meditationSessions = await ctx.db
      .query('meditationSession')
      .filter((q) => q.eq(q.field('userId'), userId))
      .order('desc')
      .take(7);

    // Calculate sleep score (40% weight)
    let sleepScore = 0;
    if (sleepEntries.length > 0) {
      const avgDuration =
        sleepEntries.reduce((sum, entry) => sum + entry.durationHours, 0) /
        sleepEntries.length;
      const avgQuality =
        sleepEntries.reduce((sum, entry) => sum + entry.qualityRating, 0) /
        sleepEntries.length;

      // Duration score (0-100)
      const durationScore = Math.min(100, (avgDuration / 8) * 100);

      // Quality score (0-100)
      const qualityScore = (avgQuality / 5) * 100;

      sleepScore = durationScore * 0.6 + qualityScore * 0.4;
    }

    // Calculate mood score (40% weight)
    let moodScore = 0;
    if (moodEntries.length > 0) {
      // Count positive moods
      const positiveMoods = moodEntries.filter((entry) =>
        ['happy', 'excited', 'calm'].includes(entry.mood),
      ).length;

      const positiveRatio = positiveMoods / moodEntries.length;

      // Mood score based on positive ratio
      moodScore = positiveRatio * 100;
    }

    // Calculate meditation score (20% weight)
    let meditationScore = 0;
    if (meditationSessions.length > 0) {
      const totalMinutes = meditationSessions.reduce(
        (sum, session) => sum + session.durationMinutes,
        0,
      );
      const completionRate =
        meditationSessions.filter((session) => session.completed).length /
        meditationSessions.length;

      // Score based on total minutes (max 100 for 60+ minutes)
      const minutesScore = Math.min(100, (totalMinutes / 60) * 100);

      // Score based on completion rate
      const completionScore = completionRate * 100;

      meditationScore = minutesScore * 0.7 + completionScore * 0.3;
    }

    // Calculate overall wellbeing score
    let overallScore = 0;
    let weightSum = 0;

    if (sleepEntries.length > 0) {
      overallScore += sleepScore * 0.4;
      weightSum += 0.4;
    }

    if (moodEntries.length > 0) {
      overallScore += moodScore * 0.4;
      weightSum += 0.4;
    }

    if (meditationSessions.length > 0) {
      overallScore += meditationScore * 0.2;
      weightSum += 0.2;
    }

    // If no data, return default score
    if (weightSum === 0) {
      return {
        score: 70,
        sleepScore: 0,
        moodScore: 0,
        meditationScore: 0,
        dataPoints: 0,
      };
    }

    // Normalize score based on available data
    overallScore = overallScore / weightSum;

    return {
      score: Math.round(overallScore),
      sleepScore: Math.round(sleepScore),
      moodScore: Math.round(moodScore),
      meditationScore: Math.round(meditationScore),
      dataPoints:
        sleepEntries.length + moodEntries.length + meditationSessions.length,
    };
  },
});
