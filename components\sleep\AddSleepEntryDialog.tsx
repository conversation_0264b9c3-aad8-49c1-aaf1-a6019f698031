import { StyleSheet, View } from 'react-native';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { Button } from '../ui/button';
import { Text } from '../ui/text';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { formatDate } from '@/lib/utils';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

const AddSleepEntryDialog = () => {
  const addSleepEntry = useMutation(api.sleep.addSleepEntry);

  const [date, setDate] = useState(
    formatDate(new Date().toISOString().split('T')[0]),
  );
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [qualityRating, setQualityRating] = useState('');
  const [notes, setNotes] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  const bottomSheetRef = useRef<BottomSheet>(null);

  const handleSheetChange = useCallback((index: number) => {
    console.log('handleSheetChange', index);
  }, []);
  const handleSnapPress = useCallback((index: number) => {
    bottomSheetRef.current?.snapToIndex(index);
  }, []);
  const handleClosePress = useCallback(() => {
    bottomSheetRef.current?.close();
  }, []);

  const snapPoints = useMemo(() => ['25%', '50%', '90%'], []);

  const handleAddSleep = async () => {
    try {
      const startDateTime = new Date(`${date}T${startTime}:00`);
      const endDateTime = new Date(`${date}T${endTime}:00`);

      // Handle overnight sleep (e.g., sleep at 10 PM, wake up at 6 AM next day)
      if (endDateTime < startDateTime) {
        endDateTime.setDate(endDateTime.getDate() + 1);
      }

      const durationMs = endDateTime.getTime() - startDateTime.getTime();
      const durationHours = durationMs / (1000 * 60 * 60);

      await addSleepEntry({
        date: new Date(date).toISOString().split('T')[0], // Ensure date is ISO string for Convex
        startTime: startDateTime.toISOString(),
        endTime: endDateTime.toISOString(),
        durationHours: parseFloat(durationHours.toFixed(2)),
        qualityRating: parseInt(qualityRating),
        notes: notes || undefined,
      });

      // Reset form
      setDate(formatDate(new Date().toISOString().split('T')[0]));
      setStartTime('');
      setEndTime('');
      setQualityRating('');
      setNotes('');
      setIsOpen(false); // Close sheet on success
    } catch (error) {
      console.error('Failed to add sleep entry:', error);
      // TODO: Show error to user
    }
  };

  return (
    <GestureHandlerRootView style={styles.container}>
      <Button
        className="w-full"
        variant="default"
        onPress={() => handleSnapPress(1)}
      >
        <Text>Add Sleep Entry</Text>
      </Button>

      <BottomSheet
        ref={bottomSheetRef}
        snapPoints={snapPoints}
        index={-1}
        enableDynamicSizing={false}
        onChange={handleSheetChange}
      >
        <BottomSheetScrollView contentContainerStyle={styles.contentContainer}>
          <View className="px-4">
            <Text className="text-2xl font-bold mb-2">Add Sleep Entry</Text>
            <Text className="text-gray-500 mb-4">
              Record your sleep details for the day.
            </Text>
            <View className="gap-4 py-4">
              <View className="gap-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  value={date}
                  onChangeText={setDate}
                  placeholder="YYYY-MM-DD"
                />
              </View>
              <View className="gap-2">
                <Label htmlFor="startTime">Start Time</Label>
                <Input
                  id="startTime"
                  value={startTime}
                  onChangeText={setStartTime}
                  placeholder="HH:MM (e.g., 22:00)"
                />
              </View>
              <View className="gap-2">
                <Label htmlFor="endTime">End Time</Label>
                <Input
                  id="endTime"
                  value={endTime}
                  onChangeText={setEndTime}
                  placeholder="HH:MM (e.g., 06:00)"
                />
              </View>
              <View className="gap-2">
                <Label htmlFor="qualityRating">Quality Rating (1-5)</Label>
                <Input
                  id="qualityRating"
                  value={qualityRating}
                  onChangeText={setQualityRating}
                  keyboardType="numeric"
                  placeholder="e.g., 4"
                />
              </View>
              <View className="gap-2">
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Input
                  id="notes"
                  value={notes}
                  onChangeText={setNotes}
                  placeholder="Any observations about your sleep"
                />
              </View>
            </View>
            <View className="flex-row justify-end gap-2 mt-4">
              <Button variant="outline" onPress={() => setIsOpen(false)}>
                <Text>Cancel</Text>
              </Button>
              <Button onPress={handleAddSleep}>
                <Text>Save Sleep</Text>
              </Button>
            </View>
          </View>
        </BottomSheetScrollView>
      </BottomSheet>
    </GestureHandlerRootView>
  );
};

export default AddSleepEntryDialog;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 1000, // Ensure it's above other elements
  },
  contentContainer: {
    paddingBottom: 20,
    flex: 1,
  },
  bottomSheetBackground: {
    backgroundColor: '#f0f0f0', // Adjust as needed for your theme
  },
  bottomSheetHandle: {
    backgroundColor: '#ccc', // Adjust as needed for your theme
  },
});
