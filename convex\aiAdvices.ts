import { query } from './_generated/server';
import { v } from 'convex/values';

export const getAdvicesByDate = query({
  args: { date: v.string() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    return await ctx.db
      .query('aiMealAdvices')
      .filter((q) => q.eq(q.field('userId'), identity.subject))
      .filter((q) => q.eq(q.field('date'), args.date))
      .collect();
  },
});
