import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card } from '../ui/card';
import { Text } from '../ui/text';
import { Progress } from '../ui/progress';

export function CalorieCard() {
  return (
    <Card style={styles.card}>
      <View style={styles.header}>
        <Text style={styles.title}>Calories</Text>
        <Text style={styles.subtitle}>1575/2267 Kcal</Text>
      </View>

      <Progress value={70} style={styles.progress} />

      <View style={styles.macros}>
        <MacroItem label="Protein" value="100g" percent={63} />
        <MacroItem label="Carbs" value="34g" percent={57} />
        <MacroItem label="Fat" value="22g" percent={28} />
      </View>
    </Card>
  );
}

function MacroItem({
  label,
  value,
  percent,
}: {
  label: string;
  value: string;
  percent: number;
}) {
  return (
    <View style={styles.macroItem}>
      <Text style={styles.macroLabel}>{label}</Text>
      <View style={styles.macroProgressContainer}>
        <Progress
          value={percent}
          style={[styles.macroProgress, { backgroundColor: '#f0f0f0' }]}
        />
      </View>
      <Text style={styles.macroValue}>{value}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    padding: 16,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  progress: {
    height: 8,
    marginBottom: 24,
  },
  macros: {
    gap: 16,
  },
  macroItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  macroLabel: {
    width: 60,
    fontSize: 14,
  },
  macroProgressContainer: {
    flex: 1,
  },
  macroProgress: {
    height: 6,
  },
  macroValue: {
    width: 40,
    fontSize: 14,
    textAlign: 'right',
  },
});
