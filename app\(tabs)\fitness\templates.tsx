import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { theme } from '@/constants/theme';
import { <PERSON><PERSON><PERSON>, <PERSON>, Zap, Flower } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Header } from '@/components/ui/Header';

type Exercise = {
  name: string;
  sets: number;
  reps?: number;
  duration?: number;
  restTime?: number;
  weight?: number;
};

type WorkoutTemplate = {
  id: string;
  name: string;
  type: string;
  duration: number;
  description: string;
  icon: React.ReactNode;
  exercises: Exercise[];
};

const workoutTemplates: WorkoutTemplate[] = [
  {
    id: 'beginner-strength',
    name: 'Beginner Strength',
    type: 'Strength',
    duration: 30,
    description: 'Perfect for those starting their strength training journey',
    icon: <Dumbbell size={32} color={theme.colors.primary} />,
    exercises: [
      { name: 'Push-ups', sets: 3, reps: 8, restTime: 60 },
      { name: 'Bodyweight Squats', sets: 3, reps: 12, restTime: 60 },
      { name: 'Plank', sets: 3, duration: 30, restTime: 30 },
      { name: 'Lunges', sets: 3, reps: 10, restTime: 45 },
      { name: 'Mountain Climbers', sets: 3, reps: 15, restTime: 30 },
    ],
  },
  {
    id: 'cardio-blast',
    name: 'Cardio Blast',
    type: 'Cardio',
    duration: 25,
    description: 'High-intensity cardio workout to boost your heart rate',
    icon: <Heart size={32} color={theme.colors.accent} />,
    exercises: [
      { name: 'Jumping Jacks', sets: 4, reps: 30, restTime: 30 },
      { name: 'High Knees', sets: 4, reps: 20, restTime: 30 },
      { name: 'Burpees', sets: 3, reps: 8, restTime: 45 },
      { name: 'Running in Place', sets: 4, duration: 60, restTime: 30 },
      { name: 'Jump Squats', sets: 3, reps: 12, restTime: 45 },
    ],
  },
  {
    id: 'hiit-power',
    name: 'HIIT Power',
    type: 'HIIT',
    duration: 20,
    description: 'Quick and intense workout for maximum results',
    icon: <Zap size={32} color={theme.colors.error} />,
    exercises: [
      { name: 'Burpees', sets: 4, reps: 10, restTime: 30 },
      { name: 'Jump Squats', sets: 4, reps: 15, restTime: 30 },
      { name: 'Push-ups', sets: 4, reps: 12, restTime: 30 },
      { name: 'Mountain Climbers', sets: 4, reps: 20, restTime: 30 },
      { name: 'Plank Jacks', sets: 4, reps: 15, restTime: 30 },
    ],
  },
  {
    id: 'flexibility-flow',
    name: 'Flexibility Flow',
    type: 'Flexibility',
    duration: 35,
    description: 'Gentle stretching routine for improved flexibility',
    icon: <Flower size={32} color={theme.colors.secondary} />,
    exercises: [
      { name: 'Cat-Cow Stretch', sets: 3, duration: 30, restTime: 15 },
      { name: 'Downward Dog', sets: 3, duration: 30, restTime: 15 },
      { name: "Child's Pose", sets: 3, duration: 45, restTime: 15 },
      { name: 'Pigeon Pose', sets: 2, duration: 60, restTime: 20 },
      { name: 'Seated Forward Fold', sets: 3, duration: 30, restTime: 15 },
    ],
  },
  {
    id: 'upper-body-strength',
    name: 'Upper Body Strength',
    type: 'Strength',
    duration: 40,
    description: 'Focus on building upper body strength and muscle',
    icon: <Dumbbell size={32} color={theme.colors.primary} />,
    exercises: [
      { name: 'Push-ups', sets: 4, reps: 12, restTime: 60 },
      { name: 'Pike Push-ups', sets: 3, reps: 8, restTime: 60 },
      { name: 'Tricep Dips', sets: 3, reps: 10, restTime: 45 },
      { name: 'Plank', sets: 3, duration: 45, restTime: 30 },
      { name: 'Superman', sets: 3, reps: 15, restTime: 45 },
      { name: 'Wall Handstand', sets: 3, duration: 20, restTime: 45 },
    ],
  },
  {
    id: 'lower-body-power',
    name: 'Lower Body Power',
    type: 'Strength',
    duration: 35,
    description: 'Build strong legs and glutes with this focused workout',
    icon: <Dumbbell size={32} color={theme.colors.primary} />,
    exercises: [
      { name: 'Squats', sets: 4, reps: 15, restTime: 60 },
      { name: 'Lunges', sets: 3, reps: 12, restTime: 45 },
      { name: 'Single-leg Glute Bridges', sets: 3, reps: 10, restTime: 45 },
      { name: 'Calf Raises', sets: 4, reps: 20, restTime: 30 },
      { name: 'Wall Sit', sets: 3, duration: 45, restTime: 30 },
    ],
  },
];

export default function WorkoutTemplates() {
  const router = useRouter();
  const createWorkout = useMutation(api.fitness.createWorkout);

  const handleUseTemplate = async (template: WorkoutTemplate) => {
    try {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      await createWorkout({
        name: template.name,
        type: template.type,
        description: template.description,
        scheduledDate: tomorrow.toISOString(),
        estimatedDuration: template.duration,
        exercises: template.exercises.map((exercise) => {
          // Create base exercise object with required fields
          const baseExercise = {
            name: exercise.name,
            sets: exercise.sets,
            reps: exercise.reps ?? 12, // Default to 12 reps if not specified
            restTime: exercise.restTime ?? 30, // Default 30 seconds rest time
            weight: exercise.weight,
            duration: exercise.duration,
          };

          return baseExercise;
        }),
      });

      router.push('/fitness/schedule');
    } catch (error) {
      console.error('Error creating workout from template:', error);
      // Consider showing an error toast to the user
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Strength':
        return theme.colors.primary;
      case 'Cardio':
        return theme.colors.accent;
      case 'HIIT':
        return theme.colors.error;
      case 'Flexibility':
        return theme.colors.secondary;
      default:
        return theme.colors.gray[500];
    }
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Header showBackButton title="Workout Templates" />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.subtitle}>
            Choose from pre-designed workouts to get started quickly
          </Text>
        </View>

        <View style={styles.templatesGrid}>
          {workoutTemplates.map((template, index) => (
            <View key={template.id} style={styles.templateCard}>
              <View style={styles.templateHeader}>
                <View style={styles.templateIcon}>{template.icon}</View>
                <View style={styles.templateInfo}>
                  <Text style={styles.templateName}>{template.name}</Text>
                  <View style={styles.templateMeta}>
                    <View
                      style={[
                        styles.typeBadge,
                        { backgroundColor: `${getTypeColor(template.type)}20` },
                      ]}
                    >
                      <Text
                        style={[
                          styles.typeText,
                          { color: getTypeColor(template.type) },
                        ]}
                      >
                        {template.type}
                      </Text>
                    </View>
                    <Text style={styles.duration}>{template.duration} min</Text>
                  </View>
                </View>
              </View>

              <Text style={styles.templateDescription}>
                {template.description}
              </Text>

              <View style={styles.exercisesList}>
                <Text style={styles.exercisesTitle}>Exercises:</Text>
                {template.exercises.slice(0, 3).map((exercise, idx) => (
                  <Text key={idx} style={styles.exerciseItem}>
                    • {exercise.name} ({exercise.sets} sets)
                  </Text>
                ))}
                {template.exercises.length > 3 && (
                  <Text style={styles.moreExercises}>
                    +{template.exercises.length - 3} more exercises
                  </Text>
                )}
              </View>

              <TouchableOpacity
                style={styles.useButton}
                onPress={() => handleUseTemplate(template)}
              >
                <Text style={styles.useButtonText}>Use Template</Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>

        <View style={styles.customSection}>
          <Text style={styles.customTitle}>Want something different?</Text>
          <Text style={styles.customText}>
            Create your own custom workout with exercises tailored to your
            goals.
          </Text>
          <TouchableOpacity
            style={styles.customButton}
            onPress={() => router.push('/fitness/create-workout')}
          >
            <Text style={styles.customButtonText}>Create Custom Workout</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  content: {
    flex: 1,
    padding: theme.spacing.l,
  },
  header: {
    marginBottom: theme.spacing.xl,
  },

  subtitle: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
    textAlign: 'center',
  },
  templatesGrid: {
    gap: theme.spacing.l,
    marginBottom: theme.spacing.xl,
  },
  templateCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    ...theme.shadows.medium,
  },
  templateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.m,
  },
  templateIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: theme.colors.gray[50],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.m,
  },
  templateInfo: {
    flex: 1,
  },
  templateName: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.xs,
  },
  templateMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.m,
  },
  typeBadge: {
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.m,
  },
  typeText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.xs,
  },
  duration: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
  },
  templateDescription: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[700],
    marginBottom: theme.spacing.m,
    lineHeight: 20,
  },
  exercisesList: {
    marginBottom: theme.spacing.l,
  },
  exercisesTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.s,
  },
  exerciseItem: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[700],
    marginBottom: theme.spacing.xs,
  },
  moreExercises: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.primary,
    marginTop: theme.spacing.xs,
  },
  useButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
    alignItems: 'center',
  },
  useButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
  },
  customSection: {
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.xl,
    alignItems: 'center',
  },
  customTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.s,
  },
  customText: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
    textAlign: 'center',
    marginBottom: theme.spacing.l,
  },
  customButton: {
    backgroundColor: theme.colors.secondary,
    paddingVertical: theme.spacing.m,
    paddingHorizontal: theme.spacing.xl,
    borderRadius: theme.borderRadius.m,
  },
  customButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
  },
});
