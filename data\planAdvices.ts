export interface PlanAdvice {
  text: string;
  message: string; // Explanation for the advice or action
  icon: string; // Emoji or icon name
  color: string; // Hex or Tailwind color
}

export interface PlanAdvicesAndActions {
  advices: PlanAdvice[];
  recommendedActions: PlanAdvice[];
}

export const planAdvices: Record<string, PlanAdvicesAndActions> = {
  weight: {
    advices: [
      {
        text: 'Track your meals daily',
        message:
          'Recording meals helps you stay accountable and spot eating patterns.',
        icon: '📝',
        color: '#34d399',
      },
      {
        text: 'Stay hydrated',
        message:
          'Drinking enough water supports metabolism and reduces unnecessary snacking.',
        icon: '💧',
        color: '#60a5fa',
      },
      {
        text: 'Aim for gradual, sustainable weight loss',
        message:
          'Slow weight loss is healthier and more likely to last long-term.',
        icon: '📉',
        color: '#fbbf24',
      },
      {
        text: 'Avoid skipping meals',
        message:
          'Skipping meals can slow metabolism and lead to overeating later.',
        icon: '⏰',
        color: '#f87171',
      },
      {
        text: 'Limit sugary snacks',
        message:
          'Reducing sugar helps prevent weight gain and supports stable energy.',
        icon: '🍬',
        color: '#f472b6',
      },
    ],
    recommendedActions: [
      {
        text: 'Exercise at least 3 times a week',
        message: 'Regular activity burns calories and improves overall health.',
        icon: '🏃‍♂️',
        color: '#f59e42',
      },
      {
        text: 'Prioritize whole foods',
        message:
          'Whole foods are more filling and nutritious than processed foods.',
        icon: '🥗',
        color: '#10b981',
      },
      {
        text: 'Get enough sleep',
        message:
          'Adequate sleep helps regulate hunger hormones and supports weight loss.',
        icon: '🛌',
        color: '#6366f1',
      },
    ],
  },
  heart: {
    advices: [
      {
        text: 'Limit salt and processed foods',
        message: 'Too much salt raises blood pressure and harms heart health.',
        icon: '🧂',
        color: '#f87171',
      },
      {
        text: 'Eat more fruits and vegetables',
        message:
          'Fruits and veggies provide fiber and nutrients for a healthy heart.',
        icon: '🍎',
        color: '#34d399',
      },
      {
        text: 'Choose healthy fats',
        message:
          'Healthy fats like olive oil lower bad cholesterol and protect your heart.',
        icon: '🥑',
        color: '#fbbf24',
      },
      {
        text: 'Reduce red meat intake',
        message: 'Less red meat lowers saturated fat and heart disease risk.',
        icon: '🥩',
        color: '#f472b6',
      },
    ],
    recommendedActions: [
      {
        text: 'Engage in regular aerobic exercise',
        message:
          'Aerobic activity strengthens your heart and improves circulation.',
        icon: '🚴‍♂️',
        color: '#60a5fa',
      },
      {
        text: 'Monitor blood pressure',
        message:
          'Regular checks help catch issues early and prevent complications.',
        icon: '🩺',
        color: '#fbbf24',
      },
      {
        text: 'Quit smoking',
        message: 'Stopping smoking greatly reduces your risk of heart disease.',
        icon: '🚭',
        color: '#f87171',
      },
    ],
  },
  prenatal: {
    advices: [
      {
        text: 'Eat a variety of nutrient-rich foods',
        message: 'A diverse diet supports your baby’s growth and your health.',
        icon: '🥦',
        color: '#34d399',
      },
      {
        text: 'Take prenatal vitamins',
        message:
          'Prenatal vitamins ensure you get key nutrients like folic acid.',
        icon: '💊',
        color: '#fbbf24',
      },
      {
        text: 'Stay hydrated',
        message:
          'Proper hydration supports healthy blood flow and fetal development.',
        icon: '💧',
        color: '#60a5fa',
      },
      {
        text: 'Avoid raw or undercooked foods',
        message:
          'Raw foods can carry bacteria that are risky during pregnancy.',
        icon: '🍣',
        color: '#f87171',
      },
    ],
    recommendedActions: [
      {
        text: 'Consult your doctor regularly',
        message:
          'Regular checkups help monitor your health and your baby’s progress.',
        icon: '👩‍⚕️',
        color: '#6366f1',
      },
      {
        text: 'Engage in light physical activity',
        message:
          'Gentle exercise can boost mood and support a healthy pregnancy.',
        icon: '🚶‍♀️',
        color: '#f59e42',
      },
    ],
  },
  sports: {
    advices: [
      {
        text: 'Increase protein intake',
        message:
          'Protein helps repair muscles and supports athletic performance.',
        icon: '🍗',
        color: '#fbbf24',
      },
      {
        text: 'Stay hydrated',
        message: 'Hydration is key for energy and recovery during sports.',
        icon: '💧',
        color: '#60a5fa',
      },
      {
        text: 'Eat balanced meals',
        message:
          'Balanced meals provide the energy and nutrients needed for training.',
        icon: '🥗',
        color: '#10b981',
      },
      {
        text: 'Refuel after workouts',
        message: 'Eating after exercise helps muscles recover and grow.',
        icon: '🥤',
        color: '#f472b6',
      },
    ],
    recommendedActions: [
      {
        text: 'Warm up before exercise',
        message: 'Warming up prepares your body and reduces injury risk.',
        icon: '🤸‍♂️',
        color: '#f59e42',
      },
      {
        text: 'Track your progress',
        message: 'Tracking helps you see improvements and stay motivated.',
        icon: '📈',
        color: '#6366f1',
      },
    ],
  },
  recovery: {
    advices: [
      {
        text: 'Eat protein-rich foods',
        message: 'Protein supports tissue repair and speeds up recovery.',
        icon: '🥚',
        color: '#fbbf24',
      },
      {
        text: 'Stay hydrated',
        message: 'Hydration helps your body heal and function properly.',
        icon: '💧',
        color: '#60a5fa',
      },
      {
        text: 'Include fruits and vegetables',
        message: 'Vitamins and minerals from produce aid the healing process.',
        icon: '🍎',
        color: '#34d399',
      },
      {
        text: 'Avoid alcohol',
        message: 'Alcohol can slow healing and interfere with medications.',
        icon: '🚫',
        color: '#f87171',
      },
    ],
    recommendedActions: [
      {
        text: 'Follow your doctor’s advice',
        message: 'Medical guidance ensures safe and effective recovery.',
        icon: '👨‍⚕️',
        color: '#6366f1',
      },
      {
        text: 'Rest as needed',
        message: 'Rest allows your body to repair and regain strength.',
        icon: '🛌',
        color: '#f59e42',
      },
    ],
  },
  senior: {
    advices: [
      {
        text: 'Focus on nutrient-dense foods',
        message:
          'Nutrient-rich foods help maintain strength and prevent deficiencies.',
        icon: '🥦',
        color: '#34d399',
      },
      {
        text: 'Stay hydrated',
        message:
          'Older adults are at higher risk of dehydration, so drink water often.',
        icon: '💧',
        color: '#60a5fa',
      },
      {
        text: 'Limit salt and sugar',
        message: 'Reducing salt and sugar supports heart and metabolic health.',
        icon: '🧂',
        color: '#f87171',
      },
      {
        text: 'Eat enough fiber',
        message: 'Fiber aids digestion and helps prevent constipation.',
        icon: '🌾',
        color: '#fbbf24',
      },
    ],
    recommendedActions: [
      {
        text: 'Stay physically active',
        message: 'Regular movement maintains mobility and independence.',
        icon: '🚶‍♂️',
        color: '#f59e42',
      },
      {
        text: 'Get regular checkups',
        message: 'Checkups help catch health issues early for better outcomes.',
        icon: '🩺',
        color: '#6366f1',
      },
    ],
  },
  gluten: {
    advices: [
      {
        text: 'Read food labels carefully',
        message: 'Labels help you avoid hidden gluten in packaged foods.',
        icon: '🔖',
        color: '#fbbf24',
      },
      {
        text: 'Avoid wheat, barley, and rye',
        message:
          'These grains contain gluten and should be excluded from your diet.',
        icon: '🚫',
        color: '#f87171',
      },
      {
        text: 'Choose certified gluten-free products',
        message:
          'Certified products are tested to be safe for gluten-free diets.',
        icon: '✅',
        color: '#34d399',
      },
    ],
    recommendedActions: [
      {
        text: 'Plan meals ahead',
        message: 'Planning helps you avoid accidental gluten exposure.',
        icon: '📅',
        color: '#f59e42',
      },
      {
        text: 'Consult a dietitian',
        message:
          'A dietitian can help you meet nutrition needs without gluten.',
        icon: '👩‍⚕️',
        color: '#6366f1',
      },
    ],
  },
  diabetes: {
    advices: [
      {
        text: 'Monitor carbohydrate intake',
        message: 'Carbs affect blood sugar, so tracking helps with control.',
        icon: '🍞',
        color: '#fbbf24',
      },
      {
        text: 'Eat regular meals',
        message: 'Consistent meals help maintain stable blood sugar levels.',
        icon: '⏰',
        color: '#60a5fa',
      },
      {
        text: 'Limit sugary foods',
        message: 'Too much sugar can spike blood glucose and worsen diabetes.',
        icon: '🍬',
        color: '#f472b6',
      },
      {
        text: 'Stay active',
        message:
          'Physical activity helps lower blood sugar and improve insulin use.',
        icon: '🏃‍♂️',
        color: '#10b981',
      },
    ],
    recommendedActions: [
      {
        text: 'Check blood sugar regularly',
        message:
          'Frequent checks help you manage diabetes and avoid complications.',
        icon: '🩸',
        color: '#f87171',
      },
      {
        text: 'Follow medication schedule',
        message: 'Taking medicine as prescribed keeps blood sugar in check.',
        icon: '💊',
        color: '#6366f1',
      },
    ],
  },
  gut: {
    advices: [
      {
        text: 'Eat fiber-rich foods',
        message:
          'Fiber supports healthy digestion and regular bowel movements.',
        icon: '🌾',
        color: '#fbbf24',
      },
      {
        text: 'Include probiotics',
        message: 'Probiotics help balance gut bacteria for better digestion.',
        icon: '🦠',
        color: '#34d399',
      },
      {
        text: 'Stay hydrated',
        message: 'Water aids digestion and prevents constipation.',
        icon: '💧',
        color: '#60a5fa',
      },
      {
        text: 'Limit processed foods',
        message: 'Processed foods can disrupt gut health and cause discomfort.',
        icon: '🚫',
        color: '#f87171',
      },
    ],
    recommendedActions: [
      {
        text: 'Chew food thoroughly',
        message: 'Chewing well helps your body digest food more easily.',
        icon: '😋',
        color: '#f59e42',
      },
      {
        text: 'Manage stress',
        message: 'Stress can upset your gut, so relaxation is important.',
        icon: '🧘‍♂️',
        color: '#6366f1',
      },
    ],
  },
  vegan: {
    advices: [
      {
        text: 'Eat a variety of plant foods',
        message:
          'Diverse plant foods provide all the nutrients your body needs.',
        icon: '🥦',
        color: '#34d399',
      },
      {
        text: 'Ensure adequate protein',
        message:
          'Plant proteins like beans and tofu help maintain muscle and health.',
        icon: '🌱',
        color: '#10b981',
      },
      {
        text: 'Supplement vitamin B12',
        message: 'B12 is not found in plants, so supplements are essential.',
        icon: '💊',
        color: '#fbbf24',
      },
      {
        text: 'Plan meals for balance',
        message:
          'Balanced vegan meals prevent deficiencies and support energy.',
        icon: '📅',
        color: '#f59e42',
      },
    ],
    recommendedActions: [
      {
        text: 'Read labels for hidden animal products',
        message:
          'Some foods contain animal ingredients not obvious from the name.',
        icon: '🔖',
        color: '#6366f1',
      },
      {
        text: 'Consult a dietitian',
        message:
          'A dietitian can help you meet all your nutrition needs on a vegan diet.',
        icon: '👩‍⚕️',
        color: '#60a5fa',
      },
    ],
  },
  muscle: {
    advices: [
      {
        text: 'Increase protein intake',
        message: 'Protein is essential for muscle growth and repair.',
        icon: '🍗',
        color: '#fbbf24',
      },
      {
        text: 'Eat frequent meals',
        message: 'Frequent meals provide steady nutrients for muscle building.',
        icon: '⏰',
        color: '#60a5fa',
      },
      {
        text: 'Stay hydrated',
        message: 'Hydration supports muscle function and recovery.',
        icon: '💧',
        color: '#34d399',
      },
      {
        text: 'Get enough sleep',
        message: 'Sleep is when your muscles recover and grow.',
        icon: '🛌',
        color: '#6366f1',
      },
    ],
    recommendedActions: [
      {
        text: 'Strength train regularly',
        message:
          'Consistent strength training builds and maintains muscle mass.',
        icon: '🏋️‍♂️',
        color: '#f59e42',
      },
      {
        text: 'Track your progress',
        message: 'Tracking helps you adjust your routine for better results.',
        icon: '📈',
        color: '#10b981',
      },
    ],
  },
  skin: {
    advices: [
      {
        text: 'Eat antioxidant-rich foods',
        message:
          'Antioxidants protect skin from damage and support a healthy glow.',
        icon: '🍓',
        color: '#f472b6',
      },
      {
        text: 'Stay hydrated',
        message: 'Hydration keeps your skin supple and reduces dryness.',
        icon: '💧',
        color: '#60a5fa',
      },
      {
        text: 'Limit processed foods',
        message: 'Processed foods can trigger skin issues like acne.',
        icon: '🚫',
        color: '#f87171',
      },
      {
        text: 'Include healthy fats',
        message: 'Healthy fats nourish your skin and help prevent dryness.',
        icon: '🥑',
        color: '#10b981',
      },
    ],
    recommendedActions: [
      {
        text: 'Use sunscreen daily',
        message: 'Sunscreen protects your skin from sun damage and aging.',
        icon: '🧴',
        color: '#fbbf24',
      },
      {
        text: 'Get enough sleep',
        message: 'Sleep allows your skin to repair and look its best.',
        icon: '🛌',
        color: '#6366f1',
      },
    ],
  },
  detox: {
    advices: [
      {
        text: 'Drink plenty of water',
        message:
          'Water helps flush toxins and keeps your body functioning well.',
        icon: '💧',
        color: '#60a5fa',
      },
      {
        text: 'Eat fruits and vegetables',
        message:
          'Produce provides antioxidants that support your body’s detox systems.',
        icon: '🍎',
        color: '#34d399',
      },
      {
        text: 'Limit caffeine and alcohol',
        message:
          'Reducing these helps your liver and kidneys detoxify efficiently.',
        icon: '🚫',
        color: '#f87171',
      },
    ],
    recommendedActions: [
      {
        text: 'Get enough rest',
        message: 'Rest allows your body to recover and detox naturally.',
        icon: '🛌',
        color: '#6366f1',
      },
      {
        text: 'Engage in light activity',
        message: 'Gentle movement supports circulation and detoxification.',
        icon: '🚶‍♂️',
        color: '#f59e42',
      },
    ],
  },
  mediterranean: {
    advices: [
      {
        text: 'Eat more fruits and vegetables',
        message:
          'Produce is a staple of the Mediterranean diet and supports heart health.',
        icon: '🍅',
        color: '#34d399',
      },
      {
        text: 'Use olive oil as main fat',
        message: 'Olive oil provides healthy fats that protect your heart.',
        icon: '🫒',
        color: '#fbbf24',
      },
      {
        text: 'Include fish regularly',
        message:
          'Fish offers omega-3s that reduce inflammation and support health.',
        icon: '🐟',
        color: '#60a5fa',
      },
      {
        text: 'Limit red meat',
        message:
          'Less red meat means less saturated fat and better heart health.',
        icon: '🥩',
        color: '#f87171',
      },
    ],
    recommendedActions: [
      {
        text: 'Enjoy meals with family',
        message:
          'Sharing meals supports emotional well-being and healthy habits.',
        icon: '👨‍👩‍👧‍👦',
        color: '#10b981',
      },
      {
        text: 'Be physically active',
        message:
          'Regular activity is a key part of the Mediterranean lifestyle.',
        icon: '🚶‍♂️',
        color: '#f59e42',
      },
    ],
  },
  keto: {
    advices: [
      {
        text: 'Limit carbs, focus on healthy fats',
        message: 'Low carbs and healthy fats help your body enter ketosis.',
        icon: '🥑',
        color: '#10b981',
      },
      {
        text: 'Monitor ketone levels',
        message:
          'Tracking ketones ensures you’re in the right metabolic state.',
        icon: '🧪',
        color: '#6366f1',
      },
      {
        text: 'Stay hydrated',
        message: 'Hydration helps prevent keto side effects like headaches.',
        icon: '💧',
        color: '#60a5fa',
      },
      {
        text: 'Watch for nutrient deficiencies',
        message:
          'A varied diet prevents vitamin and mineral shortages on keto.',
        icon: '🔬',
        color: '#fbbf24',
      },
    ],
    recommendedActions: [
      {
        text: 'Plan meals ahead',
        message: 'Planning helps you stick to keto and avoid hidden carbs.',
        icon: '📅',
        color: '#f59e42',
      },
      {
        text: 'Consult a healthcare provider',
        message: 'Medical advice ensures keto is safe and effective for you.',
        icon: '👩‍⚕️',
        color: '#6366f1',
      },
    ],
  },
  intermittent: {
    advices: [
      {
        text: 'Stick to your eating window',
        message: 'Consistent eating times help your body adjust to fasting.',
        icon: '⏰',
        color: '#fbbf24',
      },
      {
        text: 'Stay hydrated during fasting',
        message:
          'Water helps you feel full and prevents dehydration while fasting.',
        icon: '💧',
        color: '#60a5fa',
      },
      {
        text: 'Break your fast with balanced meals',
        message: 'Balanced meals after fasting restore energy and nutrients.',
        icon: '🥗',
        color: '#10b981',
      },
    ],
    recommendedActions: [
      {
        text: 'Avoid overeating after fasting',
        message: 'Overeating can undo fasting benefits and cause discomfort.',
        icon: '🚫',
        color: '#f87171',
      },
      {
        text: 'Listen to your body',
        message:
          'Paying attention to hunger and fullness cues prevents problems.',
        icon: '🧘‍♂️',
        color: '#6366f1',
      },
    ],
  },
  dash: {
    advices: [
      {
        text: 'Eat more fruits and vegetables',
        message: 'Produce is rich in nutrients that help lower blood pressure.',
        icon: '🍎',
        color: '#34d399',
      },
      {
        text: 'Limit salt and sodium',
        message:
          'Less salt helps control blood pressure and supports heart health.',
        icon: '🧂',
        color: '#f87171',
      },
      {
        text: 'Choose low-fat dairy',
        message: 'Low-fat dairy provides calcium without excess saturated fat.',
        icon: '🥛',
        color: '#60a5fa',
      },
    ],
    recommendedActions: [
      {
        text: 'Read nutrition labels',
        message: 'Labels help you spot hidden salt and unhealthy ingredients.',
        icon: '🔖',
        color: '#fbbf24',
      },
      {
        text: 'Be physically active',
        message:
          'Exercise helps lower blood pressure and improves heart health.',
        icon: '🚶‍♂️',
        color: '#f59e42',
      },
    ],
  },
  paleo: {
    advices: [
      {
        text: 'Focus on lean meats and vegetables',
        message:
          'These foods are staples of the paleo diet and support health.',
        icon: '🥩',
        color: '#fbbf24',
      },
      {
        text: 'Avoid processed foods',
        message: 'Processed foods are not paleo and can harm your health.',
        icon: '🚫',
        color: '#f87171',
      },
      {
        text: 'Eat nuts and seeds',
        message: 'Nuts and seeds provide healthy fats and protein for energy.',
        icon: '🥜',
        color: '#10b981',
      },
    ],
    recommendedActions: [
      {
        text: 'Plan meals ahead',
        message:
          'Planning helps you stick to paleo and avoid non-compliant foods.',
        icon: '📅',
        color: '#f59e42',
      },
      {
        text: 'Stay active',
        message: 'Physical activity is a key part of the paleo lifestyle.',
        icon: '🏃‍♂️',
        color: '#60a5fa',
      },
    ],
  },
};
