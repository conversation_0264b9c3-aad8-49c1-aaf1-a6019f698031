import { StyleSheet, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Header } from '@/components/ui/Header';
import { ScrollView } from 'react-native';
import {
  Scale,
  Heart,
  Baby,
  Dumbbell,
  Stethoscope,
  UserCog,
  Wheat,
  Activity,
  Syringe, // For Diabetes
  Leaf, // For Vegan
  Bone, // For Muscle Gain
  Soup, // For Gut Health
  Sparkles, // For Skin Enhancement
  Droplet,
  Weight,
  PlusCircle, // For Detox & Cleanse
} from 'lucide-react-native';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { healthPlansData, HealthPlanType } from '@/data/healthPlansData';
import { iconWithClassName } from '@/lib/icons/iconWithClassName';
import { Doc } from '@/convex/_generated/dataModel';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { router } from 'expo-router';

type HealthPlan = Doc<'healthPlan'>;

iconWithClassName(Scale);
iconWithClassName(Heart);
iconWithClassName(Baby);
iconWithClassName(Dumbbell);
iconWithClassName(Stethoscope);
iconWithClassName(UserCog);
iconWithClassName(Wheat);
iconWithClassName(Activity);
iconWithClassName(Syringe);
iconWithClassName(Leaf);
iconWithClassName(Bone);
iconWithClassName(Soup);
iconWithClassName(Sparkles);
iconWithClassName(Droplet);

const EditPlan = () => {
  const allHealthPlans = useQuery(api.healthPlans.getHealthPlans);
  const activePlan = useQuery(api.healthPlans.getActiveHealthPlan);

  const updateHealthPlanMutation = useMutation(
    api.healthPlans.updateHealthPlan,
  );
  const createHealthPlanMutation = useMutation(
    api.healthPlans.createHealthPlan,
  );

  const selectPlan = async (plan: HealthPlan) => {
    if (activePlan?._id === plan._id) {
      return;
    }
    await updateHealthPlanMutation({
      id: plan._id,
      updates: { isActive: true },
    });
  };

  const getPlanDetails = (type: HealthPlanType) => {
    const plan = healthPlansData.find((p) => p.type === type);
    if (plan) {
      let iconComponent;
      switch (type) {
        case 'weight':
          iconComponent = <Scale size={24} className="text-primary" />;
          break;
        case 'heart':
          iconComponent = <Heart size={24} className="text-primary" />;
          break;
        case 'prenatal':
          iconComponent = <Baby size={24} className="text-primary" />;
          break;
        case 'sports':
          iconComponent = <Dumbbell size={24} className="text-primary" />;
          break;
        case 'recovery':
          iconComponent = <Stethoscope size={24} className="text-primary" />;
          break;
        case 'senior':
          iconComponent = <UserCog size={24} className="text-primary" />;
          break;
        case 'gluten':
          iconComponent = <Wheat size={24} className="text-primary" />;
          break;
        case 'diabetes':
          iconComponent = <Syringe size={24} className="text-primary" />;
          break;
        case 'gut':
          iconComponent = <Soup size={24} className="text-primary" />;
          break;
        case 'vegan':
          iconComponent = <Leaf size={24} className="text-primary" />;
          break;
        case 'muscle':
          iconComponent = <Bone size={24} className="text-primary" />;
          break;
        case 'skin':
          iconComponent = <Sparkles size={24} className="text-primary" />;
          break;
        case 'detox':
          iconComponent = <Droplet size={24} className="text-primary" />;
          break;
        default:
          iconComponent = null;
      }
      return {
        title: plan.title,
        description: plan.description,
        icon: iconComponent,
      };
    }
    return {
      title: 'Unknown Plan',
      description: 'No description available for this plan type.',
      icon: null,
    };
  };

  return (
    <SafeAreaView edges={['top']} className="flex-1">
      <Header
        title="Select Health Plan"
        showBackButton
        rightElement={
          <TouchableOpacity onPress={() => router.push('/health/new-plan')}>
            <PlusCircle />
          </TouchableOpacity>
        }
      />
      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        {allHealthPlans && allHealthPlans.length > 0 ? (
          allHealthPlans.map((plan) => {
            const details = getPlanDetails(plan.type);
            return (
              <TouchableOpacity
                key={plan._id}
                className={cn(
                  'flex-row items-center p-3 rounded-md mb-2 bg-secondary',
                  activePlan?._id === plan._id && 'bg-primary/10 border-r-4',
                )}
                onPress={() => selectPlan(plan)}
              >
                <View className="w-12 h-12 rounded-full bg-card justify-center items-center shadow-sm">
                  {details.icon}
                </View>
                <View className="flex-1 ml-4">
                  <Text className="font-bold text-base text-foreground mb-1">
                    {details.title}
                  </Text>
                  <Text className="font-normal text-sm text-muted-foreground">
                    {details.description}
                  </Text>
                </View>
              </TouchableOpacity>
            );
          })
        ) : (
          <View className="flex-1 items-center justify-center py-16">
            <View className="mb-4 bg-card rounded-full p-4 shadow-sm">
              <Sparkles size={40} className="text-primary" />
            </View>
            <Text className="font-bold text-lg text-foreground mb-2 text-center">
              No Health Plans Yet
            </Text>
            <Text className="text-muted-foreground text-center mb-4 max-w-xs">
              You have not created any health plans. Start your wellness journey
              by creating your first plan!
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default EditPlan;

const styles = StyleSheet.create({});
