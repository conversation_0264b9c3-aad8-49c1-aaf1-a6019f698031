import { Stack } from 'expo-router';

export default function FitnessLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: 'Fitness Dashboard',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="schedule"
        options={{
          title: 'Workout Schedule',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="create-workout"
        options={{
          title: 'Create Workout',
          headerShown: false,
          presentation: 'modal',
        }}
      />
      <Stack.Screen
        name="workout"
        options={{
          title: 'Workout Details',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="templates"
        options={{
          title: 'Workout Templates',
          headerShown: false,
          presentation: 'modal',
        }}
      />
    </Stack>
  );
}
