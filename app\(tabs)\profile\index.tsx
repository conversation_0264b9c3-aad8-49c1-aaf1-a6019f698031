import React from 'react';
import { ScrollView, View } from 'react-native';
import { Avatar, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { H4 } from '@/components/ui/typography';
import { useAuth, useUser } from '@clerk/clerk-expo';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Header } from '@/components/ui/Header';
import { Text } from '@/components/ui/text';
import { Card, CardContent } from '@/components/ui/card';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function ProfileScreen() {
  //

  const { signOut } = useAuth();
  const { user } = useUser();
  const router = useRouter();

  return (
    <SafeAreaView edges={['top']} className="flex-1">
      <Header title="Profile" />
      <ScrollView showsVerticalScrollIndicator={false}>
        <View className="flex-row items-center p-6">
          <Avatar alt="profile picture" className="w-20 h-20 rounded-full">
            <AvatarImage
              source={{
                uri: user?.imageUrl,
              }}
            />
          </Avatar>

          <View className="ml-6">
            <Text className="text-3xl font-bold mb-1">{user?.firstName}</Text>
            <Text className="text-sm text-muted-foreground">
              {user?.emailAddresses[0].emailAddress}
            </Text>
          </View>
        </View>

        <View className="mx-6 mt-6">
          <H4 className="mb-4">Personalization</H4>
          <Card className="border-none border-0">
            <CardContent className="p-0">
              <Button
                variant="ghost"
                className="flex-row items-center justify-start py-3 px-4"
                onPress={() => router.push('/')}
              >
                <Ionicons
                  name="heart-dislike-outline"
                  size={20}
                  color="#f97316"
                />
                <Text className="ml-3">Allergies & diet</Text>
              </Button>

              <Button
                variant="ghost"
                className="flex-row items-center justify-start py-3 px-4"
                onPress={() => router.push('/meal-planner' as any)}
              >
                <Ionicons name="calendar-outline" size={20} color="#0ea5e9" />
                <Text className="ml-3">Meal planner</Text>
              </Button>

              <Button
                variant="ghost"
                className="flex-row items-center justify-start py-3 px-4"
                onPress={() => router.push('/gym-plan' as any)}
              >
                <Ionicons name="hand-right" size={20} color="#a855f7" />
                <Text className="ml-3">Gym plan</Text>
              </Button>

              <Button
                variant="ghost"
                className="flex-row items-center justify-start py-3 px-4"
                onPress={() => router.push('/profile/update-profile' as any)}
              >
                <Ionicons name="person-circle-outline" size={20} color="#10b981" />
                <Text className="ml-3">Update Profile</Text>
              </Button>
            </CardContent>
          </Card>

          <H4 className="mb-4 mt-6">About Ray</H4>
          <Card className="border-none border-0">
            <CardContent className="p-0">
              <Button
                variant="ghost"
                className="flex-row items-center justify-start py-3 px-4"
                onPress={() => router.push('https://ray.app' as any)}
              >
                <Ionicons name="globe-outline" size={20} color="#2563eb" />
                <Text className="ml-3">Visit Website</Text>
              </Button>

              <Button
                variant="ghost"
                className="flex-row items-center justify-start py-3 px-4"
              >
                <Ionicons name="git-branch-outline" size={20} color="#ca8a04" />
                <Text className="ml-3">Version 0.0.1</Text>
              </Button>
            </CardContent>
          </Card>
        </View>

        <Button
          onPress={() => signOut()}
          variant={'destructive'}
          className="flex-row m-6"
        >
          <Ionicons name="log-out-outline" size={20} color="white" />
          <View className="ml-2" />
          <Text className="text-sm font-medium">Sign Out</Text>
        </Button>
        <View className="flex-row justify-between items-center p-6 mt-6 border-t border-border">
          <Text className="text-xs">SnapChef AI v1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
