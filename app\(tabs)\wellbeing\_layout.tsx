import { Stack } from 'expo-router';

export default function WellbeingLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: 'Wellbeing Dashboard',
        }}
      />
      <Stack.Screen
        name="sleep"
        options={{
          title: 'Sleep Tracking',
        }}
      />
      <Stack.Screen
        name="mood"
        options={{
          title: 'Mood Tracking',
        }}
      />
      <Stack.Screen
        name="meditation"
        options={{
          title: 'Meditation',
        }}
      />
      <Stack.Screen
        name="insights"
        options={{
          title: 'AI Insights',
        }}
      />
    </Stack>
  );
}
