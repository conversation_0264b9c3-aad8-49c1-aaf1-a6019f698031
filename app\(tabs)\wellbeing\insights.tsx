import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Brain, TrendingUp, TrendingDown, CircleAlert as AlertCircle, CircleCheck as CheckCircle, Lightbulb, Target, Calendar, ChevronLeft, Moon, Heart, Activity } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { LineChart } from 'react-native-gifted-charts';

const { width } = Dimensions.get('window');

export default function AIInsights() {
  const router = useRouter();

  // Get data for insights
  const recentMoodEntries = useQuery(api.mood.getMoodEntries, {
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });

  const moodSummary = useQuery(api.mood.getMoodSummary, {
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });

  const averageSleepDuration = useQuery(api.sleep.getAverageSleepDuration, {
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });

  const lastSleepEntry = useQuery(api.sleep.getLastSleepEntry);

  // Generate AI insights based on data
  const generateInsights = () => {
    const insights = [];

    // Sleep insights
    if (averageSleepDuration !== undefined) {
      if (averageSleepDuration < 7) {
        insights.push({
          type: 'warning',
          category: 'Sleep',
          title: 'Sleep Duration Below Recommended',
          description: `Your average sleep is ${averageSleepDuration.toFixed(1)} hours. Aim for 7-9 hours for optimal health.`,
          recommendation: 'Try setting a consistent bedtime routine and avoiding screens 1 hour before bed.',
          icon: Moon,
          color: '#f59e0b',
          priority: 'high',
        });
      } else if (averageSleepDuration >= 8) {
        insights.push({
          type: 'success',
          category: 'Sleep',
          title: 'Excellent Sleep Habits',
          description: `Great job! You're averaging ${averageSleepDuration.toFixed(1)} hours of sleep.`,
          recommendation: 'Keep maintaining your current sleep schedule for continued wellness.',
          icon: Moon,
          color: '#22c55e',
          priority: 'low',
        });
      }
    }

    // Mood insights
    if (moodSummary?.averageIntensity !== undefined) {
      if (moodSummary.averageIntensity < 5) {
        insights.push({
          type: 'warning',
          category: 'Mood',
          title: 'Mood Patterns Need Attention',
          description: `Your average mood intensity is ${moodSummary.averageIntensity.toFixed(1)}/10, which is below optimal.`,
          recommendation: 'Consider incorporating more mood-boosting activities like exercise, meditation, or social connections.',
          icon: Heart,
          color: '#ef4444',
          priority: 'high',
        });
      } else if (moodSummary.averageIntensity >= 7) {
        insights.push({
          type: 'success',
          category: 'Mood',
          title: 'Positive Mood Trends',
          description: `Your mood has been consistently positive with an average of ${moodSummary.averageIntensity.toFixed(1)}/10.`,
          recommendation: 'Continue the activities and habits that are contributing to your positive mood.',
          icon: Heart,
          color: '#22c55e',
          priority: 'low',
        });
      }
    }

    // Sleep quality insights
    if (lastSleepEntry?.qualityRating !== undefined) {
      if (lastSleepEntry.qualityRating <= 2) {
        insights.push({
          type: 'warning',
          category: 'Sleep Quality',
          title: 'Poor Sleep Quality Detected',
          description: `Your last sleep quality rating was ${lastSleepEntry.qualityRating}/5.`,
          recommendation: 'Consider factors like room temperature, noise levels, and stress before bedtime.',
          icon: Moon,
          color: '#ef4444',
          priority: 'medium',
        });
      }
    }

    // Data tracking insights
    if (recentMoodEntries && recentMoodEntries.length < 7) {
      insights.push({
        type: 'info',
        category: 'Tracking',
        title: 'Increase Data Tracking',
        description: `You've logged ${recentMoodEntries.length} mood entries this month.`,
        recommendation: 'Regular tracking helps identify patterns and improve your wellbeing insights.',
        icon: Activity,
        color: '#06b6d4',
        priority: 'medium',
      });
    }

    // General wellness insight
    insights.push({
      type: 'tip',
      category: 'Wellness',
      title: 'Daily Wellness Tip',
      description: 'Small consistent actions lead to big improvements in mental health.',
      recommendation: 'Try the 5-4-3-2-1 grounding technique: Notice 5 things you see, 4 you can touch, 3 you hear, 2 you smell, 1 you taste.',
      icon: Lightbulb,
      color: '#8b5cf6',
      priority: 'low',
    });

    return insights.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority as keyof typeof priorityOrder] - priorityOrder[a.priority as keyof typeof priorityOrder];
    });
  };

  const insights = generateInsights();

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'success':
        return CheckCircle;
      case 'warning':
        return AlertCircle;
      case 'info':
        return TrendingUp;
      case 'tip':
        return Lightbulb;
      default:
        return Brain;
    }
  };

  const InsightCard = ({ insight }: { insight: any }) => {
    const IconComponent = getInsightIcon(insight.type);
    const CategoryIcon = insight.icon;

    return (
      <View className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden mb-4">
        <LinearGradient
          colors={[`${insight.color}20`, `${insight.color}10`]}
          className="p-6"
        >
          <View className="flex-row items-start justify-between mb-3">
            <View className="flex-row items-center flex-1">
              <View 
                className="w-10 h-10 rounded-xl items-center justify-center mr-3"
                style={{ backgroundColor: `${insight.color}30` }}
              >
                <CategoryIcon size={20} color={insight.color} />
              </View>
              <View className="flex-1">
                <View className="flex-row items-center mb-1">
                  <Text className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    {insight.category}
                  </Text>
                  <View className="ml-2">
                    <IconComponent size={14} color={insight.color} />
                  </View>
                </View>
                <Text className="text-lg font-semibold text-gray-900">
                  {insight.title}
                </Text>
              </View>
            </View>
          </View>

          <Text className="text-gray-700 mb-4 leading-relaxed">
            {insight.description}
          </Text>

          <View className="bg-white/60 rounded-xl p-4">
            <View className="flex-row items-start">
              <Target size={16} color={insight.color} className="mt-0.5 mr-2" />
              <Text className="text-gray-800 font-medium flex-1">
                {insight.recommendation}
              </Text>
            </View>
          </View>
        </LinearGradient>
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <View className="flex-row items-center px-6 py-4 bg-white border-b border-gray-200">
        <TouchableOpacity onPress={() => router.back()} className="mr-4">
          <ChevronLeft size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-xl font-semibold text-gray-900">AI Insights</Text>
      </View>

      <ScrollView className="flex-1 px-6 py-6">
        {/* Header */}
        <View className="mb-6">
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            className="rounded-3xl p-6"
          >
            <View className="flex-row items-center justify-between">
              <View className="flex-1">
                <Text className="text-white text-xl font-bold mb-2">
                  Personalized Insights
                </Text>
                <Text className="text-white/80 text-sm">
                  AI-powered recommendations based on your wellbeing data
                </Text>
              </View>
              <View className="w-16 h-16 rounded-full bg-white/20 items-center justify-center">
                <Brain size={28} color="white" />
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Insights Summary */}
        <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-100">
          <Text className="text-xl font-semibold text-gray-900 mb-4">Summary</Text>
          
          <View className="flex-row justify-between">
            <View className="items-center flex-1">
              <Text className="text-2xl font-bold text-red-500">
                {insights.filter(i => i.priority === 'high').length}
              </Text>
              <Text className="text-gray-600 text-sm">High Priority</Text>
            </View>
            <View className="items-center flex-1">
              <Text className="text-2xl font-bold text-orange-500">
                {insights.filter(i => i.priority === 'medium').length}
              </Text>
              <Text className="text-gray-600 text-sm">Medium Priority</Text>
            </View>
            <View className="items-center flex-1">
              <Text className="text-2xl font-bold text-green-500">
                {insights.filter(i => i.type === 'success').length}
              </Text>
              <Text className="text-gray-600 text-sm">Positive Trends</Text>
            </View>
          </View>
        </View>

        {/* Mood Trend Chart */}
        {recentMoodEntries && recentMoodEntries.length > 0 && (
          <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-100">
            <Text className="text-xl font-semibold text-gray-900 mb-4">Mood Trend Analysis</Text>
            
            <LineChart
              data={recentMoodEntries.slice(-14).map((entry, index) => ({
                value: entry.intensity || 5,
                label: index % 3 === 0 ? new Date(entry.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) : '',
              }))}
              width={width - 80}
              height={160}
              color="#667eea"
              thickness={3}
              dataPointsColor="#667eea"
              dataPointsRadius={4}
              hideRules
              xAxisThickness={0}
              yAxisThickness={0}
              yAxisTextStyle={{ color: '#9ca3af', fontSize: 12 }}
              xAxisLabelTextStyle={{ color: '#9ca3af', fontSize: 12 }}
              curved
              areaChart
              startFillColor="#667eea"
              startOpacity={0.3}
              endOpacity={0.1}
              maxValue={10}
              noOfSections={5}
            />
            
            <Text className="text-gray-600 text-sm mt-2 text-center">
              Your mood intensity over the past 2 weeks
            </Text>
          </View>
        )}

        {/* Insights List */}
        <View className="mb-6">
          <Text className="text-xl font-semibold text-gray-900 mb-4">
            Your Insights ({insights.length})
          </Text>
          
          {insights.map((insight, index) => (
            <InsightCard key={index} insight={insight} />
          ))}
        </View>

        {/* Action Items */}
        <View className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <Text className="text-xl font-semibold text-gray-900 mb-4">Recommended Actions</Text>
          
          <View className="space-y-3">
            <TouchableOpacity className="flex-row items-center p-3 bg-blue-50 rounded-xl">
              <Calendar size={20} color="#2563eb" />
              <Text className="ml-3 text-blue-800 font-medium flex-1">
                Set a consistent sleep schedule
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity className="flex-row items-center p-3 bg-green-50 rounded-xl">
              <Heart size={20} color="#16a34a" />
              <Text className="ml-3 text-green-800 font-medium flex-1">
                Practice daily mood check-ins
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity className="flex-row items-center p-3 bg-purple-50 rounded-xl">
              <Brain size={20} color="#7c3aed" />
              <Text className="ml-3 text-purple-800 font-medium flex-1">
                Try 5-minute daily meditation
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}