import { mutation } from './_generated/server';
import { healthPlansData } from '../data/healthPlansData';

// Migration to add nutrition data to existing health plans
export const migrateHealthPlansWithNutrition = mutation({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get all health plans that don't have nutrition data
    const healthPlans = await ctx.db
      .query('healthPlan')
      .filter((q) => q.eq(q.field('userId'), identity.subject))
      .collect();

    let updatedCount = 0;

    for (const plan of healthPlans) {
      // Skip if already has nutrition data
      if (plan.dailyNutrition) {
        continue;
      }

      // Find the corresponding nutrition data from healthPlansData
      const planData = healthPlansData.find(p => p.type === plan.type);
      
      if (planData) {
        await ctx.db.patch(plan._id, {
          dailyNutrition: planData.dailyNutrition
        });
        updatedCount++;
      }
    }

    return {
      success: true,
      message: `Updated ${updatedCount} health plans with nutrition data`,
      updatedCount
    };
  },
});

// Migration to add nutrition data to all health plans (admin function)
export const migrateAllHealthPlansWithNutrition = mutation({
  handler: async (ctx) => {
    // Get all health plans that don't have nutrition data
    const healthPlans = await ctx.db
      .query('healthPlan')
      .collect();

    let updatedCount = 0;

    for (const plan of healthPlans) {
      // Skip if already has nutrition data
      if (plan.dailyNutrition) {
        continue;
      }

      // Find the corresponding nutrition data from healthPlansData
      const planData = healthPlansData.find(p => p.type === plan.type);
      
      if (planData) {
        await ctx.db.patch(plan._id, {
          dailyNutrition: planData.dailyNutrition
        });
        updatedCount++;
      }
    }

    return {
      success: true,
      message: `Updated ${updatedCount} health plans with nutrition data`,
      updatedCount
    };
  },
});
