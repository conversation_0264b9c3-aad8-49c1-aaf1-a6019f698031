import { Stack } from 'expo-router';

export default function ExerciseLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: 'Exercise Plans',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="create"
        options={{
          title: 'Create Plan',
          headerShown: false,
          presentation: 'modal',
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: 'Plan Details',
          headerShown: false,
        }}
      />
    </Stack>
  );
}
