import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card } from '../ui/card';
import { Text } from '../ui/text';
import { Badge } from '../ui/badge';

export function DailyPlanCard() {
  return (
    <Card style={styles.card}>
      <View style={styles.header}>
        <Text style={styles.title}>Daily Plan</Text>
        <Badge variant="secondary">Upcoming</Badge>
      </View>

      <View style={styles.planItems}>
        <PlanItem
          title="Yoga Group"
          time="14:00-15:00"
          location="A5 room"
          intensity="Medium"
          trainer="Tiffany Way"
        />
        <PlanItem
          title="Balance"
          time="15:30-16:30"
          location="A2 room"
          intensity="Light"
        />
      </View>
    </Card>
  );
}

function PlanItem({
  title,
  time,
  location,
  intensity,
  trainer,
}: {
  title: string;
  time: string;
  location: string;
  intensity: string;
  trainer?: string;
}) {
  return (
    <View style={styles.planItem}>
      <View style={styles.planInfo}>
        <Text style={styles.planTitle}>{title}</Text>
        <Text style={styles.planDetails}>
          {time} • {location}
        </Text>
        {trainer && <Text style={styles.trainer}>Trainer: {trainer}</Text>}
      </View>
      <Badge>{intensity}</Badge>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    padding: 16,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  planItems: {
    gap: 16,
  },
  planItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  planInfo: {
    gap: 4,
  },
  planTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  planDetails: {
    fontSize: 14,
    opacity: 0.7,
  },
  trainer: {
    fontSize: 14,
    opacity: 0.7,
  },
});
