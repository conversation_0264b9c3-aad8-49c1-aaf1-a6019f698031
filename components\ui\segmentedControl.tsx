import * as React from "react";
import { View, Pressable } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Text } from "@/components/ui/text";
import { cn } from "@/lib/utils";

export type SegmentedControlOption = {
  value: string;
  label: string;
  icon?: React.ComponentProps<typeof MaterialCommunityIcons>["name"];
};

export interface SegmentedControlProps {
  value: string;
  onValueChange: (value: string) => void;
  options: SegmentedControlOption[];
}

export function SegmentedControl({
  value,
  onValueChange,
  options,
}: SegmentedControlProps) {
  return (
    <View className="flex-row border border-border rounded-md overflow-hidden">
      {options.map((option) => {
        const selected = option.value === value;
        return (
          <Pressable
            key={option.value}
            onPress={() => onValueChange(option.value)}
            className={cn(
              "flex-1 p-2 items-center justify-center",
              selected ? "bg-primary" : "bg-background"
            )}
          >
            {option.icon && (
              <MaterialCommunityIcons
                name={option.icon}
                size={20}
                className={cn(
                  selected ? "text-primary-foreground" : "text-foreground"
                )}
              />
            )}
            <Text
              className={cn(
                "mt-1",
                selected ? "text-primary-foreground" : "text-foreground"
              )}
            >
              {option.label}
            </Text>
          </Pressable>
        );
      })}
    </View>
  );
}
