import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { theme } from '@/constants/theme';
import { Plus, Minus, Save, Calendar } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Header } from '@/components/ui/Header';

type Exercise = {
  name: string;
  sets: number;
  reps: number;
  weight?: number;
  duration?: number;
  restTime?: number;
};

const workoutTypes = ['Strength', 'Cardio', 'Flexibility', 'HIIT', 'Mixed'];

export default function CreateWorkout() {
  const router = useRouter();
  const createWorkout = useMutation(api.fitness.createWorkout);

  const [name, setName] = useState('');
  const [type, setType] = useState('Strength');
  const [description, setDescription] = useState('');
  const [scheduledDate, setScheduledDate] = useState(new Date());
  const [estimatedDuration, setEstimatedDuration] = useState('30');
  const [exercises, setExercises] = useState<Exercise[]>([
    { name: '', sets: 3, reps: 12 },
  ]);
  const [showDatePicker, setShowDatePicker] = useState(false);

  const addExercise = () => {
    setExercises([...exercises, { name: '', sets: 3, reps: 12 }]);
  };

  const removeExercise = (index: number) => {
    if (exercises.length > 1) {
      setExercises(exercises.filter((_, i) => i !== index));
    }
  };

  const updateExercise = (index: number, field: keyof Exercise, value: any) => {
    const updated = [...exercises];
    updated[index] = { ...updated[index], [field]: value };
    setExercises(updated);
  };

  const handleSave = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a workout name');
      return;
    }

    if (exercises.some((e) => !e.name.trim())) {
      Alert.alert('Error', 'Please fill in all exercise names');
      return;
    }

    try {
      await createWorkout({
        name: name.trim(),
        type,
        description: description.trim(),
        scheduledDate: scheduledDate.toISOString(),
        estimatedDuration: parseInt(estimatedDuration) || 30,
        exercises: exercises.filter((e) => e.name.trim()),
      });

      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to create workout');
    }
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Header showBackButton title="New workout" />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Basic Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Workout Details</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Name</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder="Enter workout name"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Type</Text>
            <View style={styles.typeSelector}>
              {workoutTypes.map((workoutType) => (
                <TouchableOpacity
                  key={workoutType}
                  style={[
                    styles.typeButton,
                    type === workoutType && styles.typeButtonActive,
                  ]}
                  onPress={() => setType(workoutType)}
                >
                  <Text
                    style={[
                      styles.typeButtonText,
                      type === workoutType && styles.typeButtonTextActive,
                    ]}
                  >
                    {workoutType}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Enter workout description"
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.row}>
            <View style={styles.halfInput}>
              <Text style={styles.label}>Scheduled Date</Text>
              <TouchableOpacity
                style={styles.dateButton}
                onPress={() => setShowDatePicker(true)}
              >
                <Calendar size={20} color={theme.colors.gray[600]} />
                <Text style={styles.dateText}>
                  {scheduledDate.toLocaleDateString()}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.halfInput}>
              <Text style={styles.label}>Duration (min)</Text>
              <TextInput
                style={styles.input}
                value={estimatedDuration}
                onChangeText={setEstimatedDuration}
                keyboardType="number-pad"
                placeholder="30"
              />
            </View>
          </View>
        </View>

        {/* Exercises */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Exercises</Text>
            <TouchableOpacity
              style={styles.addExerciseButton}
              onPress={addExercise}
            >
              <Plus size={20} color={theme.colors.primary} />
              <Text style={styles.addExerciseText}>Add Exercise</Text>
            </TouchableOpacity>
          </View>

          {exercises.map((exercise, index) => (
            <View key={index} style={styles.exerciseCard}>
              <View style={styles.exerciseHeader}>
                <Text style={styles.exerciseNumber}>Exercise {index + 1}</Text>
                {exercises.length > 1 && (
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => removeExercise(index)}
                  >
                    <Minus size={20} color={theme.colors.error} />
                  </TouchableOpacity>
                )}
              </View>

              <TextInput
                style={styles.input}
                value={exercise.name}
                onChangeText={(value) => updateExercise(index, 'name', value)}
                placeholder="Exercise name (e.g., Push-ups)"
              />

              <View style={styles.exerciseDetails}>
                <View style={styles.detailInput}>
                  <Text style={styles.detailLabel}>Sets</Text>
                  <TextInput
                    style={[styles.input, styles.numberInput]}
                    value={exercise.sets.toString()}
                    onChangeText={(value) =>
                      updateExercise(index, 'sets', parseInt(value) || 0)
                    }
                    keyboardType="number-pad"
                  />
                </View>

                <View style={styles.detailInput}>
                  <Text style={styles.detailLabel}>Reps</Text>
                  <TextInput
                    style={[styles.input, styles.numberInput]}
                    value={exercise.reps.toString()}
                    onChangeText={(value) =>
                      updateExercise(index, 'reps', parseInt(value) || 0)
                    }
                    keyboardType="number-pad"
                  />
                </View>

                <View style={styles.detailInput}>
                  <Text style={styles.detailLabel}>Weight (kg)</Text>
                  <TextInput
                    style={[styles.input, styles.numberInput]}
                    value={exercise.weight?.toString() || ''}
                    onChangeText={(value) =>
                      updateExercise(
                        index,
                        'weight',
                        parseInt(value) || undefined,
                      )
                    }
                    keyboardType="number-pad"
                    placeholder="Optional"
                  />
                </View>
              </View>

              <View style={styles.exerciseDetails}>
                <View style={styles.detailInput}>
                  <Text style={styles.detailLabel}>Duration (min)</Text>
                  <TextInput
                    style={[styles.input, styles.numberInput]}
                    value={exercise.duration?.toString() || ''}
                    onChangeText={(value) =>
                      updateExercise(
                        index,
                        'duration',
                        parseInt(value) || undefined,
                      )
                    }
                    keyboardType="number-pad"
                    placeholder="Optional"
                  />
                </View>

                <View style={styles.detailInput}>
                  <Text style={styles.detailLabel}>Rest (sec)</Text>
                  <TextInput
                    style={[styles.input, styles.numberInput]}
                    value={exercise.restTime?.toString() || ''}
                    onChangeText={(value) =>
                      updateExercise(
                        index,
                        'restTime',
                        parseInt(value) || undefined,
                      )
                    }
                    keyboardType="number-pad"
                    placeholder="60"
                  />
                </View>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Save Button */}
      <View style={styles.footer}>
        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
          <Save size={20} color={theme.colors.white} />
          <Text style={styles.saveButtonText}>Save Workout</Text>
        </TouchableOpacity>
      </View>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={scheduledDate}
          mode="date"
          display="default"
          onChange={(event, date) => {
            setShowDatePicker(false);
            if (date) setScheduledDate(date);
          }}
          minimumDate={new Date()}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    padding: theme.spacing.l,
  },
  section: {
    marginBottom: theme.spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.m,
  },
  sectionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
  },
  inputGroup: {
    marginBottom: theme.spacing.l,
  },
  label: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[800],
    marginBottom: theme.spacing.s,
  },
  input: {
    borderWidth: 1,
    borderColor: theme.colors.gray[300],
    borderRadius: theme.borderRadius.m,
    padding: theme.spacing.m,
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  typeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.s,
  },
  typeButton: {
    paddingVertical: theme.spacing.s,
    paddingHorizontal: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
    borderWidth: 1,
    borderColor: theme.colors.gray[300],
    backgroundColor: theme.colors.white,
  },
  typeButtonActive: {
    borderColor: theme.colors.primary,
    backgroundColor: `${theme.colors.primary}10`,
  },
  typeButtonText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[700],
  },
  typeButtonTextActive: {
    color: theme.colors.primary,
  },
  row: {
    flexDirection: 'row',
    gap: theme.spacing.m,
  },
  halfInput: {
    flex: 1,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.gray[300],
    borderRadius: theme.borderRadius.m,
    padding: theme.spacing.m,
  },
  dateText: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
    marginLeft: theme.spacing.s,
  },
  addExerciseButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${theme.colors.primary}10`,
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.s,
    borderRadius: theme.borderRadius.m,
  },
  addExerciseText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.primary,
    marginLeft: theme.spacing.xs,
  },
  exerciseCard: {
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginBottom: theme.spacing.m,
  },
  exerciseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.m,
  },
  exerciseNumber: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
  },
  removeButton: {
    padding: theme.spacing.xs,
  },
  exerciseDetails: {
    flexDirection: 'row',
    gap: theme.spacing.s,
    marginTop: theme.spacing.m,
  },
  detailInput: {
    flex: 1,
  },
  detailLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    marginBottom: theme.spacing.xs,
  },
  numberInput: {
    textAlign: 'center',
  },
  footer: {
    padding: theme.spacing.l,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray[200],
  },
  saveButton: {
    backgroundColor: theme.colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
  },
  saveButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    marginLeft: theme.spacing.s,
  },
});
