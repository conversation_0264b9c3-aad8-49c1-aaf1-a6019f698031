import { query, mutation } from './_generated/server';
import { v } from 'convex/values';
import { Id } from './_generated/dataModel';

// Workout Queries
export const getWorkouts = query({
  args: {
    completed: v.optional(v.boolean()),
    startDate: v.optional(v.string()),
    endDate: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    let query = ctx.db
      .query('workout')
      .filter((q) => q.eq(q.field('userId'), identity.subject));

    if (args.completed !== undefined) {
      query = query.filter((q) => q.eq(q.field('completed'), args.completed));
    }

    if (args.startDate) {
      query = query.filter((q) =>
        q.gte(q.field('scheduledDate'), args.startDate!),
      );
    }

    if (args.endDate) {
      const endOfDay = new Date(args.endDate);
      endOfDay.setHours(23, 59, 59, 999);
      query = query.filter(
        (q) => q.lte(q.field('scheduledDate'), endOfDay.toISOString()), // toISOString() already returns string, no need for !
      );
    }

    return await query.order('desc').collect();
  },
});

export const getWorkoutById = query({
  args: { id: v.id('workout') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const workout = await ctx.db.get(args.id);
    if (!workout) {
      throw new Error('Workout not found');
    }

    if (workout.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    return workout;
  },
});

export const getWeeklyStats = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    // Get start of current week (Sunday)
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    const workouts = await ctx.db
      .query('workout')
      .filter((q) =>
        q.and(
          q.eq(q.field('userId'), identity.subject),
          q.gte(q.field('scheduledDate'), startOfWeek.toISOString()),
          q.lte(q.field('scheduledDate'), endOfWeek.toISOString()),
        ),
      )
      .collect();

    const completedWorkouts = workouts.filter((w) => w.completed).length;
    const totalPlanned = workouts.length;

    // Calculate streak (consecutive days with completed workouts)
    let streakDays = 0;
    const today = new Date();
    for (let i = 0; i < 7; i++) {
      const checkDate = new Date(today);
      checkDate.setDate(today.getDate() - i);
      const dateString = checkDate.toISOString().split('T')[0];

      const hasCompletedWorkout = workouts.some(
        (w) => w.completed && w.scheduledDate.startsWith(dateString),
      );

      if (hasCompletedWorkout) {
        streakDays++;
      } else if (i > 0) {
        break; // Break streak if no workout on a previous day
      }
    }

    // Estimate calories burned (rough calculation)
    const caloriesBurned = workouts
      .filter((w) => w.completed)
      .reduce((total, w) => {
        // Rough estimate: 5-10 calories per minute based on workout type
        const multiplier = w.type === 'HIIT' ? 10 : w.type === 'Cardio' ? 8 : 5;
        return total + w.estimatedDuration * multiplier;
      }, 0);

    return {
      completedWorkouts,
      totalPlanned,
      streakDays,
      caloriesBurned,
    };
  },
});

// Workout Mutations
export const createWorkout = mutation({
  args: {
    name: v.string(),
    type: v.string(),
    description: v.optional(v.string()),
    scheduledDate: v.string(),
    estimatedDuration: v.number(),
    exercises: v.array(
      v.object({
        name: v.string(),
        sets: v.number(),
        reps: v.number(),
        weight: v.optional(v.number()),
        duration: v.optional(v.number()),
        restTime: v.optional(v.number()),
      }),
    ),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const workoutId = await ctx.db.insert('workout', {
      ...args,
      userId: identity.subject,
      completed: false,
      completedAt: undefined,
    });

    return workoutId;
  },
});

export const updateWorkout = mutation({
  args: {
    id: v.id('workout'),
    updates: v.object({
      name: v.optional(v.string()),
      type: v.optional(v.string()),
      description: v.optional(v.string()),
      scheduledDate: v.optional(v.string()),
      estimatedDuration: v.optional(v.number()),
      exercises: v.optional(
        v.array(
          v.object({
            name: v.string(),
            sets: v.number(),
            reps: v.number(),
            weight: v.optional(v.number()),
            duration: v.optional(v.number()),
            restTime: v.optional(v.number()),
          }),
        ),
      ),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const workout = await ctx.db.get(args.id);
    if (!workout) {
      throw new Error('Workout not found');
    }

    if (workout.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    await ctx.db.patch(args.id, args.updates);
    return { success: true };
  },
});

export const completeWorkout = mutation({
  args: { id: v.id('workout') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const workout = await ctx.db.get(args.id);
    if (!workout) {
      throw new Error('Workout not found');
    }

    if (workout.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    await ctx.db.patch(args.id, {
      completed: true,
      completedAt: new Date().toISOString(),
    });

    return { success: true };
  },
});

export const deleteWorkout = mutation({
  args: { id: v.id('workout') },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const workout = await ctx.db.get(args.id);
    if (!workout) {
      throw new Error('Workout not found');
    }

    if (workout.userId !== identity.subject) {
      throw new Error('Unauthorized');
    }

    await ctx.db.delete(args.id);
    return { success: true };
  },
});

// Notification-related queries
export const getUpcomingWorkouts = query({
  args: {
    days: v.optional(v.number()), // Number of days to look ahead
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    const daysAhead = args.days || 7;
    const now = new Date();
    const futureDate = new Date(now);
    futureDate.setDate(now.getDate() + daysAhead);

    const workouts = await ctx.db
      .query('workout')
      .filter((q) =>
        q.and(
          q.eq(q.field('userId'), identity.subject),
          q.eq(q.field('completed'), false),
          q.gte(q.field('scheduledDate'), now.toISOString()),
          q.lte(q.field('scheduledDate'), futureDate.toISOString()),
        ),
      )
      .order('asc')
      .collect();

    return workouts;
  },
});

export const getCompletedWorkoutsCount = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }
    const workouts = await ctx.db
      .query('workout')
      .filter((q) =>
        q.and(
          q.eq(q.field('userId'), identity.subject),
          q.eq(q.field('completed'), true),
        ),
      )
      .collect();
    return workouts.length;
  },
});
