import { Tabs, useRouter } from "expo-router";
import { StyleSheet, View, TouchableOpacity } from "react-native";
import {
  User,
  HeartPulse,
  <PERSON><PERSON><PERSON>,
  <PERSON>K<PERSON><PERSON>,
  Brain,
  MessageCircle,
} from "lucide-react-native";
import { theme } from "@/constants/theme";
import { useAuth } from "@clerk/clerk-expo";
import { Text } from "@/components/ui/text";

// Define styles after theme import but before any usage
const styles = StyleSheet.create({
  tabBarLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: 10,
    marginTop: 4,
  },
  tabBar: {
    backgroundColor: "#FFFFFF",
    borderTopWidth: 0,
    elevation: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -6 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    paddingTop: 16,
    paddingBottom: 32,
    paddingHorizontal: 24,
    height: 90,
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
  },
  chatButton: {
    position: "absolute",
    top: -25,
    left: "50%",
    marginLeft: -28,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: theme.colors.primary,
    justifyContent: "center",
    alignItems: "center",
    elevation: 12,
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    borderWidth: 3,
    borderColor: "#FFFFFF",
  },
  chatButtonGradient: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
  },
});

// Custom Tab Bar Component
const CustomTabBar = ({ state, descriptors, navigation }: any) => {
  const router = useRouter();

  const handleChatPress = () => {
    router.push("/chat");
  };

  return (
    <View style={styles.tabBar}>
      {/* Chat Button in the center */}
      <TouchableOpacity style={styles.chatButton} onPress={handleChatPress}>
        <MessageCircle size={24} color="#FFFFFF" />
      </TouchableOpacity>

      {/* Tab Items */}
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-around",
          alignItems: "center",
        }}
      >
        {state.routes.map((route: any, index: number) => {
          const { options } = descriptors[route.key];
          const label =
            options.tabBarLabel !== undefined
              ? options.tabBarLabel
              : options.title !== undefined
                ? options.title
                : route.name;
          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: "tabPress",
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          // Skip middle tab for chat button space
          if (index === 2) {
            return <View key={route.key} style={{ width: 56 }} />;
          }

          return (
            <TouchableOpacity
              key={route.key}
              onPress={onPress}
              style={{
                alignItems: "center",
                justifyContent: "center",
                paddingVertical: 8,
                paddingHorizontal: 12,
                minWidth: 60,
              }}
            >
              {options.tabBarIcon &&
                options.tabBarIcon({
                  color: isFocused
                    ? theme.colors.primary
                    : theme.colors.gray[400],
                  size: 20,
                })}
              <Text
                style={[
                  styles.tabBarLabel,
                  {
                    color: isFocused
                      ? theme.colors.primary
                      : theme.colors.gray[400],
                    fontFamily: isFocused
                      ? theme.typography.fontFamily.semiBold
                      : theme.typography.fontFamily.medium,
                  },
                ]}
              >
                {label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

export default function TabLayout() {
  const { isSignedIn } = useAuth();

  if (isSignedIn) {
    return (
      <Tabs
        tabBar={(props) => <CustomTabBar {...props} />}
        screenOptions={{
          headerShown: false,
          animation: "none",
        }}
        backBehavior="order"
      >
        <Tabs.Screen
          name="index"
          options={{
            title: "Overview",
            tabBarIcon: ({ color }) => <SquareKanban size={20} color={color} />,
          }}
        />
        <Tabs.Screen
          name="health"
          options={{
            title: "Health",
            tabBarIcon: ({ color }) => <HeartPulse size={20} color={color} />,
          }}
        />
        <Tabs.Screen
          name="wellbeing"
          options={{
            title: "Wellbeing",
            tabBarIcon: ({ color }) => <Brain size={20} color={color} />,
          }}
        />
        <Tabs.Screen
          name="fitness"
          options={{
            title: "Fitness",
            tabBarIcon: ({ color }) => <Dumbbell size={20} color={color} />,
          }}
        />
        <Tabs.Screen
          name="profile"
          options={{
            title: "Profile",
            tabBarIcon: ({ color }) => <User size={20} color={color} />,
          }}
        />
      </Tabs>
    );
  }
}
