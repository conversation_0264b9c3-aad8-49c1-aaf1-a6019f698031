import { mutation, query } from './_generated/server';
import { v } from 'convex/values';

export const createDrink = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    calories: v.number(),
    protein: v.number(),
    carbs: v.number(),
    fat: v.number(),
    // Extended nutrition fields
    fiber: v.optional(v.number()),
    sugar: v.optional(v.number()),
    sodium: v.optional(v.number()),
    water: v.optional(v.number()),
    calcium: v.optional(v.number()),
    iron: v.optional(v.number()),
    vitaminC: v.optional(v.number()),
    vitaminD: v.optional(v.number()),
    type: v.union(
      v.literal('breakfast'),
      v.literal('lunch'),
      v.literal('dinner'),
      v.literal('snacks'),
    ),
    date: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    await ctx.db.insert('drink', {
      ...args,
      userId: identity.subject,
    });
  },
});

export const getDrinksByDate = query({
  args: { date: v.string() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error('Not authenticated');
    }

    return await ctx.db
      .query('drink')
      .filter((q) => q.eq(q.field('userId'), identity.subject))
      .filter((q) => q.eq(q.field('date'), args.date))
      .collect();
  },
});
