import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { theme } from '@/constants/theme';
import {
  Play,
  CircleCheck as Check<PERSON><PERSON><PERSON>,
  Clock,
  Dumbbell,
  CreditCard as Edit,
  Trash2,
} from 'lucide-react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Header } from '@/components/ui/Header';

export default function WorkoutDetails() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const workout = useQuery(api.fitness.getWorkoutById, { id: id as any });
  const completeWorkout = useMutation(api.fitness.completeWorkout);
  const deleteWorkout = useMutation(api.fitness.deleteWorkout);

  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [completedExercises, setCompletedExercises] = useState<Set<number>>(
    new Set(),
  );

  if (!workout) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading workout...</Text>
      </View>
    );
  }

  const handleCompleteExercise = (index: number) => {
    const newCompleted = new Set(completedExercises);
    if (newCompleted.has(index)) {
      newCompleted.delete(index);
    } else {
      newCompleted.add(index);
    }
    setCompletedExercises(newCompleted);
  };

  const handleCompleteWorkout = async () => {
    try {
      await completeWorkout({ id: workout._id });
      Alert.alert('Success', 'Workout completed! Great job!', [
        { text: 'OK', onPress: () => router.back() },
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to complete workout');
    }
  };

  const handleDeleteWorkout = async () => {
    try {
      await deleteWorkout({ id: workout._id });
      setShowDeleteConfirm(false);
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to delete workout');
    }
  };

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'strength':
        return theme.colors.primary;
      case 'cardio':
        return theme.colors.accent;
      case 'flexibility':
        return theme.colors.secondary;
      case 'hiit':
        return theme.colors.error;
      default:
        return theme.colors.gray[500];
    }
  };

  const allExercisesCompleted =
    completedExercises.size === workout.exercises.length;

  return (
    <SafeAreaView style={styles.container}>
      <Header title={workout.name} showBackButton />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{workout.name}</Text>
            <View
              style={[
                styles.typeBadge,
                { backgroundColor: `${getTypeColor(workout.type)}20` },
              ]}
            >
              <Text
                style={[styles.typeText, { color: getTypeColor(workout.type) }]}
              >
                {workout.type}
              </Text>
            </View>
          </View>
          {workout.description && (
            <Text style={styles.description}>{workout.description}</Text>
          )}
        </View>

        {/* Workout Info */}
        <View style={styles.infoCard}>
          <View style={styles.infoItem}>
            <Clock size={20} color={theme.colors.primary} />
            <Text style={styles.infoText}>
              {workout.estimatedDuration} minutes
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Dumbbell size={20} color={theme.colors.primary} />
            <Text style={styles.infoText}>
              {workout.exercises.length} exercises
            </Text>
          </View>
          <View style={styles.infoItem}>
            <CheckCircle
              size={20}
              color={
                workout.completed
                  ? theme.colors.success
                  : theme.colors.gray[400]
              }
            />
            <Text style={styles.infoText}>
              {workout.completed ? 'Completed' : 'Not completed'}
            </Text>
          </View>
        </View>

        {/* Exercises */}
        <View style={styles.exercisesSection}>
          <Text style={styles.sectionTitle}>Exercises</Text>
          {workout.exercises.map((exercise, index) => {
            const isCompleted = completedExercises.has(index);
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.exerciseCard,
                  isCompleted && styles.exerciseCardCompleted,
                ]}
                onPress={() => handleCompleteExercise(index)}
              >
                <View style={styles.exerciseHeader}>
                  <Text
                    style={[
                      styles.exerciseName,
                      isCompleted && styles.exerciseNameCompleted,
                    ]}
                  >
                    {exercise.name}
                  </Text>
                  <View
                    style={[
                      styles.checkCircle,
                      isCompleted && styles.checkCircleCompleted,
                    ]}
                  >
                    {isCompleted && (
                      <CheckCircle size={20} color={theme.colors.success} />
                    )}
                  </View>
                </View>

                <View style={styles.exerciseDetails}>
                  <Text style={styles.exerciseDetail}>
                    {exercise.sets} sets × {exercise.reps} reps
                  </Text>
                  {exercise.weight && (
                    <Text style={styles.exerciseDetail}>
                      {exercise.weight} kg
                    </Text>
                  )}
                  {exercise.duration && (
                    <Text style={styles.exerciseDetail}>
                      {exercise.duration} min
                    </Text>
                  )}
                  {exercise.restTime && (
                    <Text style={styles.exerciseDetail}>
                      Rest: {exercise.restTime}s
                    </Text>
                  )}
                </View>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Progress */}
        <View style={styles.progressSection}>
          <Text style={styles.progressTitle}>Progress</Text>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${(completedExercises.size / workout.exercises.length) * 100}%`,
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {completedExercises.size} of {workout.exercises.length} exercises
            completed
          </Text>
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        {!workout.completed && (
          <TouchableOpacity
            style={[
              styles.completeButton,
              !allExercisesCompleted && styles.completeButtonDisabled,
            ]}
            onPress={handleCompleteWorkout}
            disabled={!allExercisesCompleted}
          >
            <CheckCircle size={20} color={theme.colors.white} />
            <Text style={styles.completeButtonText}>Complete Workout</Text>
          </TouchableOpacity>
        )}

        <View style={styles.secondaryActions}>
          <TouchableOpacity
            style={styles.startButton}
            onPress={() => router.push(`/workout-session/${workout._id}`)}
          >
            <Play size={20} color={theme.colors.white} />
            <Text style={styles.startButtonText}>Start Workout</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.editButton}>
            <Edit size={20} color={theme.colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => setShowDeleteConfirm(true)}
          >
            <Trash2 size={20} color={theme.colors.error} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Delete Confirmation */}
      {/* <BottomSheet
        visible={showDeleteConfirm}
        onBackButtonPress={() => setShowDeleteConfirm(false)}
        onBackdropPress={() => setShowDeleteConfirm(false)}
      >
        <View style={styles.bottomSheet}>
          <Text style={styles.bottomSheetTitle}>Delete Workout?</Text>
          <Text style={styles.bottomSheetText}>
            Are you sure you want to delete this workout? This action cannot be
            undone.
          </Text>
          <View style={styles.bottomSheetActions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowDeleteConfirm(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={handleDeleteWorkout}
            >
              <Text style={styles.confirmButtonText}>Delete</Text>
            </TouchableOpacity>
          </View>
        </View>
      </BottomSheet> */}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  content: {
    flex: 1,
    padding: theme.spacing.l,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
  },
  header: {
    marginBottom: theme.spacing.xl,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.s,
  },
  title: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xxl,
    color: theme.colors.gray[900],
    marginRight: theme.spacing.m,
    flex: 1,
  },
  typeBadge: {
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.m,
  },
  typeText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.xs,
  },
  description: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
  },
  infoCard: {
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginBottom: theme.spacing.xl,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.m,
  },
  infoText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[700],
    marginLeft: theme.spacing.s,
  },
  exercisesSection: {
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.l,
  },
  exerciseCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginBottom: theme.spacing.m,
    borderWidth: 2,
    borderColor: theme.colors.gray[200],
    ...theme.shadows.small,
  },
  exerciseCardCompleted: {
    borderColor: theme.colors.success,
    backgroundColor: `${theme.colors.success}05`,
  },
  exerciseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.s,
  },
  exerciseName: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    flex: 1,
  },
  exerciseNameCompleted: {
    textDecorationLine: 'line-through',
    color: theme.colors.gray[500],
  },
  checkCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.colors.gray[300],
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkCircleCompleted: {
    borderColor: theme.colors.success,
    backgroundColor: theme.colors.white,
  },
  exerciseDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.m,
  },
  exerciseDetail: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
  },
  progressSection: {
    marginBottom: theme.spacing.xl,
  },
  progressTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.m,
  },
  progressBar: {
    height: 8,
    backgroundColor: theme.colors.gray[200],
    borderRadius: 4,
    marginBottom: theme.spacing.s,
  },
  progressFill: {
    height: '100%',
    backgroundColor: theme.colors.success,
    borderRadius: 4,
  },
  progressText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
  },
  actionButtons: {
    padding: theme.spacing.l,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray[200],
  },
  completeButton: {
    backgroundColor: theme.colors.success,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
    marginBottom: theme.spacing.m,
  },
  completeButtonDisabled: {
    backgroundColor: theme.colors.gray[300],
  },
  completeButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    marginLeft: theme.spacing.s,
  },
  secondaryActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: theme.spacing.l,
  },
  startButton: {
    backgroundColor: theme.colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
  },
  startButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    marginLeft: theme.spacing.s,
  },
  editButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: theme.colors.error,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomSheet: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.xl,
    borderTopLeftRadius: theme.borderRadius.l,
    borderTopRightRadius: theme.borderRadius.l,
  },
  bottomSheetTitle: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.m,
    textAlign: 'center',
  },
  bottomSheetText: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
  },
  bottomSheetActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: theme.spacing.m,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: theme.colors.gray[100],
    padding: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[700],
  },
  confirmButton: {
    flex: 1,
    backgroundColor: theme.colors.error,
    padding: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
  },
});
