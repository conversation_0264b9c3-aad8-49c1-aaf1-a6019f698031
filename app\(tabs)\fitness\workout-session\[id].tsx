import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { theme } from '@/constants/theme';
import {
  Play,
  Pause,
  SkipForward,
  Volume2,
  X,
  CircleCheck as CheckCircle,
} from 'lucide-react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolateColor,
  withRepeat,
  withSequence,
} from 'react-native-reanimated';

const AnimatedView = Animated.createAnimatedComponent(View);

type WorkoutPhase = 'exercise' | 'rest' | 'completed';

export default function WorkoutSessionScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const workout = useQuery(api.fitness.getWorkoutById, { id: id as any });
  const completeWorkout = useMutation(api.fitness.completeWorkout);

  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [currentSet, setCurrentSet] = useState(1);
  const [phase, setPhase] = useState<WorkoutPhase>('exercise');
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [completedSets, setCompletedSets] = useState<Set<string>>(new Set());

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const progressValue = useSharedValue(0);
  const pulseValue = useSharedValue(1);

  // Voice synthesis for web
  const speak = (text: string) => {
    if (Platform.OS === 'web' && 'speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.8;
      utterance.pitch = 1;
      utterance.volume = 0.8;
      speechSynthesis.speak(utterance);
    }
  };

  useEffect(() => {
    if (isActive && timeRemaining > 0) {
      timerRef.current = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            handleTimerComplete();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isActive, timeRemaining]);

  useEffect(() => {
    // Animate progress
    if (phase === 'rest' && timeRemaining > 0) {
      const restTime = workout?.exercises[currentExerciseIndex]?.restTime || 60;
      progressValue.value = withTiming((restTime - timeRemaining) / restTime);
    } else if (phase === 'exercise') {
      progressValue.value = withTiming(0);
    }

    // Pulse animation during rest
    if (phase === 'rest' && isActive) {
      pulseValue.value = withRepeat(
        withSequence(
          withTiming(1.1, { duration: 500 }),
          withTiming(1, { duration: 500 }),
        ),
        -1,
        false,
      );
    } else {
      pulseValue.value = withTiming(1);
    }
  }, [phase, timeRemaining, isActive]);

  const handleTimerComplete = () => {
    setIsActive(false);

    if (phase === 'rest') {
      // Rest period completed, move to next exercise or set
      speak("Rest time over. Let's continue!");

      if (currentSet < (workout?.exercises[currentExerciseIndex]?.sets || 1)) {
        setCurrentSet((prev) => prev + 1);
        setPhase('exercise');
      } else {
        // Move to next exercise
        if (currentExerciseIndex < (workout?.exercises.length || 1) - 1) {
          setCurrentExerciseIndex((prev) => prev + 1);
          setCurrentSet(1);
          setPhase('exercise');
          speak(
            `Next exercise: ${workout?.exercises[currentExerciseIndex + 1]?.name}`,
          );
        } else {
          // Workout completed
          setPhase('completed');
          speak('Congratulations! Workout completed!');
          handleCompleteWorkout();
        }
      }
    }
  };

  const startRestTimer = () => {
    const restTime = workout?.exercises[currentExerciseIndex]?.restTime || 60;
    setTimeRemaining(restTime);
    setPhase('rest');
    setIsActive(true);
    speak(`Rest for ${restTime} seconds`);
  };

  const completeSet = () => {
    const setKey = `${currentExerciseIndex}-${currentSet}`;
    setCompletedSets((prev) => new Set([...prev, setKey]));

    if (currentSet < (workout?.exercises[currentExerciseIndex]?.sets || 1)) {
      startRestTimer();
    } else {
      // Exercise completed, move to next
      if (currentExerciseIndex < (workout?.exercises.length || 1) - 1) {
        setCurrentExerciseIndex((prev) => prev + 1);
        setCurrentSet(1);
        setPhase('exercise');
        speak(
          `Great job! Next exercise: ${workout?.exercises[currentExerciseIndex + 1]?.name}`,
        );
      } else {
        setPhase('completed');
        speak("Amazing! You've completed your workout!");
        handleCompleteWorkout();
      }
    }
  };

  const skipRest = () => {
    setIsActive(false);
    setTimeRemaining(0);
    handleTimerComplete();
  };

  const handleCompleteWorkout = async () => {
    if (workout) {
      await completeWorkout({ id: workout._id });
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: `${progressValue.value * 100}%`,
    };
  });

  const pulseStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: pulseValue.value }],
    };
  });

  const timerColorStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      progressValue.value,
      [0, 0.5, 1],
      [theme.colors.primary, theme.colors.warning, theme.colors.success],
    );
    return { backgroundColor };
  });

  if (!workout) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading workout...</Text>
      </View>
    );
  }

  const currentExercise = workout.exercises[currentExerciseIndex];

  if (phase === 'completed') {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.completedContainer}>
          <AnimatedView style={[styles.completedIcon, pulseStyle]}>
            <CheckCircle size={80} color={theme.colors.success} />
          </AnimatedView>
          <Text style={styles.completedTitle}>Workout Completed!</Text>
          <Text style={styles.completedSubtitle}>
            Great job finishing your {workout.name} workout
          </Text>
          <TouchableOpacity
            style={styles.doneButton}
            onPress={() => router.back()}
          >
            <Text style={styles.doneButtonText}>Done</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => router.back()}
        >
          <X size={24} color={theme.colors.gray[600]} />
        </TouchableOpacity>
        <Text style={styles.workoutTitle}>{workout.name}</Text>
        <TouchableOpacity
          style={styles.voiceButton}
          onPress={() => speak(currentExercise.name)}
        >
          <Volume2 size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>
            Exercise {currentExerciseIndex + 1} of {workout.exercises.length}
          </Text>
          <View style={styles.progressBarContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressBarFill,
                  {
                    width: `${(currentExerciseIndex / workout.exercises.length) * 100}%`,
                  },
                ]}
              />
            </View>
          </View>
        </View>

        {/* Current Exercise */}
        <View style={styles.exerciseContainer}>
          <Text style={styles.exerciseName}>{currentExercise.name}</Text>
          <Text style={styles.setInfo}>
            Set {currentSet} of {currentExercise.sets}
          </Text>

          <View style={styles.exerciseDetails}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Reps</Text>
              <Text style={styles.detailValue}>{currentExercise.reps}</Text>
            </View>
            {currentExercise.weight && (
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Weight</Text>
                <Text style={styles.detailValue}>
                  {currentExercise.weight} kg
                </Text>
              </View>
            )}
            {currentExercise.duration && (
              <View style={styles.detailItem}>
                <Text style={styles.detailLabel}>Duration</Text>
                <Text style={styles.detailValue}>
                  {currentExercise.duration} min
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Timer Section */}
        {phase === 'rest' && (
          <AnimatedView style={[styles.timerContainer, timerColorStyle]}>
            <AnimatedView style={pulseStyle}>
              <Text style={styles.timerTitle}>Rest Time</Text>
              <Text style={styles.timerText}>{formatTime(timeRemaining)}</Text>
            </AnimatedView>

            <View style={styles.timerProgressContainer}>
              <View style={styles.timerProgressBar}>
                <AnimatedView
                  style={[styles.timerProgressFill, progressStyle]}
                />
              </View>
            </View>

            <View style={styles.timerControls}>
              <TouchableOpacity
                style={styles.timerButton}
                onPress={() => setIsActive(!isActive)}
              >
                {isActive ? (
                  <Pause size={24} color={theme.colors.white} />
                ) : (
                  <Play size={24} color={theme.colors.white} />
                )}
              </TouchableOpacity>

              <TouchableOpacity style={styles.skipButton} onPress={skipRest}>
                <SkipForward size={20} color={theme.colors.white} />
                <Text style={styles.skipButtonText}>Skip Rest</Text>
              </TouchableOpacity>
            </View>
          </AnimatedView>
        )}

        {/* Exercise Controls */}
        {phase === 'exercise' && (
          <View style={styles.exerciseControls}>
            <TouchableOpacity
              style={styles.completeSetButton}
              onPress={completeSet}
            >
              <CheckCircle size={24} color={theme.colors.white} />
              <Text style={styles.completeSetButtonText}>Complete Set</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Completed Sets */}
        <View style={styles.setsContainer}>
          <Text style={styles.setsTitle}>Sets Progress</Text>
          <View style={styles.setsGrid}>
            {Array.from({ length: currentExercise.sets }, (_, index) => {
              const setKey = `${currentExerciseIndex}-${index + 1}`;
              const isCompleted = completedSets.has(setKey);
              const isCurrent = index + 1 === currentSet;

              return (
                <View
                  key={index}
                  style={[
                    styles.setIndicator,
                    isCompleted && styles.setIndicatorCompleted,
                    isCurrent && styles.setIndicatorCurrent,
                  ]}
                >
                  <Text
                    style={[
                      styles.setIndicatorText,
                      isCompleted && styles.setIndicatorTextCompleted,
                      isCurrent && styles.setIndicatorTextCurrent,
                    ]}
                  >
                    {index + 1}
                  </Text>
                </View>
              );
            })}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.l,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  workoutTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    flex: 1,
    textAlign: 'center',
  },
  voiceButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${theme.colors.primary}10`,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: theme.spacing.l,
  },
  progressContainer: {
    marginBottom: theme.spacing.xl,
  },
  progressText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[700],
    textAlign: 'center',
    marginBottom: theme.spacing.s,
  },
  progressBarContainer: {
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: theme.colors.gray[200],
    borderRadius: 4,
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 4,
  },
  exerciseContainer: {
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.xl,
    marginBottom: theme.spacing.xl,
    alignItems: 'center',
  },
  exerciseName: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xxl,
    color: theme.colors.gray[900],
    textAlign: 'center',
    marginBottom: theme.spacing.s,
  },
  setInfo: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.primary,
    marginBottom: theme.spacing.l,
  },
  exerciseDetails: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  detailItem: {
    alignItems: 'center',
  },
  detailLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    marginBottom: theme.spacing.xs,
  },
  detailValue: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
  },
  timerContainer: {
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.xl,
    marginBottom: theme.spacing.xl,
    alignItems: 'center',
  },
  timerTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.white,
    marginBottom: theme.spacing.s,
  },
  timerText: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: 48,
    color: theme.colors.white,
    marginBottom: theme.spacing.l,
  },
  timerProgressContainer: {
    width: '100%',
    marginBottom: theme.spacing.l,
  },
  timerProgressBar: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 4,
  },
  timerProgressFill: {
    height: '100%',
    backgroundColor: theme.colors.white,
    borderRadius: 4,
  },
  timerControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.l,
  },
  timerButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  skipButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: theme.spacing.l,
    paddingVertical: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
  },
  skipButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    marginLeft: theme.spacing.s,
  },
  exerciseControls: {
    marginBottom: theme.spacing.xl,
  },
  completeSetButton: {
    backgroundColor: theme.colors.success,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.l,
    borderRadius: theme.borderRadius.m,
  },
  completeSetButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.white,
    marginLeft: theme.spacing.s,
  },
  setsContainer: {
    marginBottom: theme.spacing.xl,
  },
  setsTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.l,
    textAlign: 'center',
  },
  setsGrid: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    gap: theme.spacing.m,
  },
  setIndicator: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.gray[200],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  setIndicatorCompleted: {
    backgroundColor: theme.colors.success,
  },
  setIndicatorCurrent: {
    borderColor: theme.colors.primary,
    backgroundColor: `${theme.colors.primary}20`,
  },
  setIndicatorText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
  },
  setIndicatorTextCompleted: {
    color: theme.colors.white,
  },
  setIndicatorTextCurrent: {
    color: theme.colors.primary,
  },
  completedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  completedIcon: {
    marginBottom: theme.spacing.xl,
  },
  completedTitle: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xxl,
    color: theme.colors.gray[900],
    textAlign: 'center',
    marginBottom: theme.spacing.m,
  },
  completedSubtitle: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[600],
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
  },
  doneButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.l,
    paddingHorizontal: theme.spacing.xxl,
    borderRadius: theme.borderRadius.m,
  },
  doneButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.white,
  },
});
