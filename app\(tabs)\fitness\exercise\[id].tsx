import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { theme } from '@/constants/theme';
import {
  Calendar,
  Clock,
  Dumbbell,
  Play,
  CheckCircle2,
  Edit2,
  Trash2,
} from 'lucide-react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import Animated, { FadeInDown } from 'react-native-reanimated';

const AnimatedView = Animated.createAnimatedComponent(View);

export default function ExercisePlanDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const plan = useQuery(api.exercisePlans.getExercisePlanById, {
    id: id as any,
  });
  const deletePlan = useMutation(api.exercisePlans.deleteExercisePlan);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  if (!plan) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading plan...</Text>
      </View>
    );
  }

  const handleDelete = async () => {
    try {
      await deletePlan({ id: plan._id });
      router.back();
    } catch (error) {
      console.error('Error deleting plan:', error);
    }
  };

  const startDate = new Date(plan.startDate);
  const endDate = new Date(plan.endDate);
  const daysRemaining = Math.ceil(
    (endDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24),
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <AnimatedView style={styles.header} entering={FadeInDown.delay(100)}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{plan.name}</Text>
            {plan.isActive && (
              <View style={styles.activeTag}>
                <Text style={styles.activeTagText}>Active</Text>
              </View>
            )}
          </View>
          <Text style={styles.description}>{plan.description}</Text>
        </AnimatedView>

        <AnimatedView style={styles.statsCard} entering={FadeInDown.delay(200)}>
          <View style={styles.statItem}>
            <Calendar size={24} color={theme.colors.primary} />
            <Text style={styles.statValue}>{daysRemaining}</Text>
            <Text style={styles.statLabel}>Days Left</Text>
          </View>

          <View style={styles.statDivider} />

          <View style={styles.statItem}>
            <Clock size={24} color={theme.colors.primary} />
            <Text style={styles.statValue}>{plan.targetDaysPerWeek}</Text>
            <Text style={styles.statLabel}>Days/Week</Text>
          </View>

          <View style={styles.statDivider} />

          <View style={styles.statItem}>
            <Dumbbell size={24} color={theme.colors.primary} />
            <Text style={styles.statValue}>{plan.exercises.length}</Text>
            <Text style={styles.statLabel}>Exercises</Text>
          </View>
        </AnimatedView>

        <AnimatedView style={styles.section} entering={FadeInDown.delay(300)}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Exercises</Text>
            <TouchableOpacity style={styles.editButton}>
              <Edit2 size={16} color={theme.colors.primary} />
              <Text style={styles.editButtonText}>Edit</Text>
            </TouchableOpacity>
          </View>

          {plan.exercises.map((exercise, index) => (
            <View key={index} style={styles.exerciseCard}>
              <Text style={styles.exerciseName}>{exercise.name}</Text>
              <View style={styles.exerciseDetails}>
                <Text style={styles.exerciseDetail}>
                  {exercise.sets} sets × {exercise.reps} reps
                </Text>
                {exercise.weight && (
                  <Text style={styles.exerciseDetail}>
                    {exercise.weight} kg
                  </Text>
                )}
              </View>
            </View>
          ))}
        </AnimatedView>

        <View style={styles.actions}>
          <TouchableOpacity
            style={styles.startButton}
            onPress={() => {
              /* Start workout session */
            }}
          >
            <Play size={20} color={theme.colors.white} />
            <Text style={styles.startButtonText}>Start Workout</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => setShowDeleteConfirm(true)}
          >
            <Trash2 size={20} color={theme.colors.error} />
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* <BottomSheet
        visible={showDeleteConfirm}
        onBackButtonPress={() => setShowDeleteConfirm(false)}
        onBackdropPress={() => setShowDeleteConfirm(false)}
      >
        <View style={styles.bottomSheet}>
          <Text style={styles.bottomSheetTitle}>Delete Plan?</Text>
          <Text style={styles.bottomSheetText}>
            Are you sure you want to delete this exercise plan? This action cannot be undone.
          </Text>
          <View style={styles.bottomSheetActions}>
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={() => setShowDeleteConfirm(false)}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.confirmButton}
              onPress={handleDelete}
            >
              <Text style={styles.confirmButtonText}>Delete</Text>
            </TouchableOpacity>
          </View>
        </View>
      </BottomSheet> */}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
  },
  header: {
    padding: theme.spacing.l,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.s,
  },
  title: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xxl,
    color: theme.colors.gray[900],
    marginRight: theme.spacing.m,
  },
  activeTag: {
    backgroundColor: `${theme.colors.success}20`,
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.m,
  },
  activeTagText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.success,
  },
  description: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
  },
  statsCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginHorizontal: theme.spacing.l,
    marginBottom: theme.spacing.xl,
    ...theme.shadows.medium,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.gray[900],
    marginTop: theme.spacing.s,
  },
  statLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    marginTop: theme.spacing.xs,
  },
  statDivider: {
    width: 1,
    height: '80%',
    backgroundColor: theme.colors.gray[200],
  },
  section: {
    padding: theme.spacing.l,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.m,
  },
  sectionTitle: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[900],
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${theme.colors.primary}10`,
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.s,
    borderRadius: theme.borderRadius.m,
  },
  editButtonText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.primary,
    marginLeft: theme.spacing.xs,
  },
  exerciseCard: {
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.m,
    padding: theme.spacing.m,
    marginBottom: theme.spacing.m,
  },
  exerciseName: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.xs,
  },
  exerciseDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  exerciseDetail: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    marginRight: theme.spacing.m,
  },
  actions: {
    flexDirection: 'row',
    padding: theme.spacing.l,
    borderTopWidth: 1,

    borderTopColor: theme.colors.gray[200],
  },
  startButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    padding: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
    marginRight: theme.spacing.m,
  },
  startButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    marginLeft: theme.spacing.s,
  },
  deleteButton: {
    width: 48,
    height: 48,
    borderRadius: theme.borderRadius.m,
    borderWidth: 1,
    borderColor: theme.colors.error,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomSheet: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.xl,
    borderTopLeftRadius: theme.borderRadius.l,
    borderTopRightRadius: theme.borderRadius.l,
  },
  bottomSheetTitle: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.m,
    textAlign: 'center',
  },
  bottomSheetText: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[600],
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
  },
  bottomSheetActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: theme.spacing.m,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: theme.colors.gray[100],
    padding: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[700],
  },
  confirmButton: {
    flex: 1,
    backgroundColor: theme.colors.error,
    padding: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
  },
});
