import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import {
  Moon,
  Clock,
  Star,
  Calendar,
  TrendingUp,
  Plus,
  ChevronLeft,
} from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';

export default function SleepTracking() {
  const router = useRouter();
  const [showForm, setShowForm] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [bedtime, setBedtime] = useState(new Date());
  const [wakeTime, setWakeTime] = useState(new Date());
  const [qualityRating, setQualityRating] = useState(3);
  const [notes, setNotes] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showBedtimePicker, setShowBedtimePicker] = useState(false);
  const [showWakeTimePicker, setShowWakeTimePicker] = useState(false);

  const addSleepEntry = useMutation(api.sleep.addSleepEntry);
  const sleepSummary = useQuery(api.sleep.getLast7DaysSleepSummary);
  const averageSleepDuration = useQuery(api.sleep.getAverageSleepDuration, {
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });

  const calculateDuration = () => {
    const bedtimeMs = bedtime.getTime();
    const wakeTimeMs = wakeTime.getTime();
    
    // Handle overnight sleep
    let duration = wakeTimeMs - bedtimeMs;
    if (duration < 0) {
      duration += 24 * 60 * 60 * 1000; // Add 24 hours
    }
    
    return duration / (1000 * 60 * 60); // Convert to hours
  };

  const handleSaveSleep = async () => {
    try {
      const duration = calculateDuration();
      
      await addSleepEntry({
        date: selectedDate.toISOString().split('T')[0],
        startTime: bedtime.toISOString(),
        endTime: wakeTime.toISOString(),
        durationHours: parseFloat(duration.toFixed(2)),
        qualityRating,
        notes: notes || undefined,
      });

      Alert.alert('Success', 'Sleep entry saved successfully!');
      setShowForm(false);
      setNotes('');
      setQualityRating(3);
    } catch (error) {
      Alert.alert('Error', 'Failed to save sleep entry');
    }
  };

  const QualityStars = () => (
    <View className="flex-row justify-center space-x-2 my-4">
      {[1, 2, 3, 4, 5].map((star) => (
        <TouchableOpacity
          key={star}
          onPress={() => setQualityRating(star)}
          className="p-2"
        >
          <Star
            size={32}
            color={star <= qualityRating ? '#f59e0b' : '#d1d5db'}
            fill={star <= qualityRating ? '#f59e0b' : 'transparent'}
          />
        </TouchableOpacity>
      ))}
    </View>
  );

  if (showForm) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50">
        <View className="flex-row items-center px-6 py-4 bg-white border-b border-gray-200">
          <TouchableOpacity onPress={() => setShowForm(false)} className="mr-4">
            <ChevronLeft size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="text-xl font-semibold text-gray-900">Log Sleep</Text>
        </View>

        <ScrollView className="flex-1 px-6 py-6">
          {/* Date Selection */}
          <View className="bg-white rounded-2xl p-4 mb-4 shadow-sm border border-gray-100">
            <Text className="text-lg font-semibold text-gray-900 mb-3">Sleep Date</Text>
            <TouchableOpacity
              onPress={() => setShowDatePicker(true)}
              className="flex-row items-center justify-between p-3 bg-gray-50 rounded-xl"
            >
              <View className="flex-row items-center">
                <Calendar size={20} color="#6b7280" />
                <Text className="ml-3 text-gray-900 font-medium">
                  {selectedDate.toLocaleDateString()}
                </Text>
              </View>
            </TouchableOpacity>
          </View>

          {/* Sleep Times */}
          <View className="bg-white rounded-2xl p-4 mb-4 shadow-sm border border-gray-100">
            <Text className="text-lg font-semibold text-gray-900 mb-3">Sleep Times</Text>
            
            <View className="space-y-3">
              <View>
                <Text className="text-gray-600 mb-2">Bedtime</Text>
                <TouchableOpacity
                  onPress={() => setShowBedtimePicker(true)}
                  className="flex-row items-center justify-between p-3 bg-gray-50 rounded-xl"
                >
                  <View className="flex-row items-center">
                    <Moon size={20} color="#6b7280" />
                    <Text className="ml-3 text-gray-900 font-medium">
                      {bedtime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>

              <View>
                <Text className="text-gray-600 mb-2">Wake Time</Text>
                <TouchableOpacity
                  onPress={() => setShowWakeTimePicker(true)}
                  className="flex-row items-center justify-between p-3 bg-gray-50 rounded-xl"
                >
                  <View className="flex-row items-center">
                    <Clock size={20} color="#6b7280" />
                    <Text className="ml-3 text-gray-900 font-medium">
                      {wakeTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>

              <View className="bg-blue-50 p-3 rounded-xl">
                <Text className="text-blue-800 font-medium text-center">
                  Duration: {calculateDuration().toFixed(1)} hours
                </Text>
              </View>
            </View>
          </View>

          {/* Sleep Quality */}
          <View className="bg-white rounded-2xl p-4 mb-4 shadow-sm border border-gray-100">
            <Text className="text-lg font-semibold text-gray-900 mb-3 text-center">
              Sleep Quality
            </Text>
            <QualityStars />
            <Text className="text-gray-600 text-center">
              {qualityRating === 1 && 'Very Poor'}
              {qualityRating === 2 && 'Poor'}
              {qualityRating === 3 && 'Fair'}
              {qualityRating === 4 && 'Good'}
              {qualityRating === 5 && 'Excellent'}
            </Text>
          </View>

          {/* Notes */}
          <View className="bg-white rounded-2xl p-4 mb-6 shadow-sm border border-gray-100">
            <Text className="text-lg font-semibold text-gray-900 mb-3">Notes (Optional)</Text>
            <TextInput
              value={notes}
              onChangeText={setNotes}
              placeholder="How did you sleep? Any factors that affected your sleep?"
              multiline
              numberOfLines={4}
              className="bg-gray-50 rounded-xl p-3 text-gray-900"
              textAlignVertical="top"
            />
          </View>

          {/* Save Button */}
          <TouchableOpacity
            onPress={handleSaveSleep}
            className="bg-purple-600 rounded-2xl p-4 items-center"
          >
            <Text className="text-white font-semibold text-lg">Save Sleep Entry</Text>
          </TouchableOpacity>
        </ScrollView>

        {/* Date/Time Pickers */}
        {showDatePicker && (
          <DateTimePicker
            value={selectedDate}
            mode="date"
            display="default"
            onChange={(event, date) => {
              setShowDatePicker(false);
              if (date) setSelectedDate(date);
            }}
          />
        )}

        {showBedtimePicker && (
          <DateTimePicker
            value={bedtime}
            mode="time"
            display="default"
            onChange={(event, time) => {
              setShowBedtimePicker(false);
              if (time) setBedtime(time);
            }}
          />
        )}

        {showWakeTimePicker && (
          <DateTimePicker
            value={wakeTime}
            mode="time"
            display="default"
            onChange={(event, time) => {
              setShowWakeTimePicker(false);
              if (time) setWakeTime(time);
            }}
          />
        )}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <View className="flex-row items-center justify-between px-6 py-4 bg-white border-b border-gray-200">
        <TouchableOpacity onPress={() => router.back()} className="mr-4">
          <ChevronLeft size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-xl font-semibold text-gray-900 flex-1">Sleep Tracking</Text>
        <TouchableOpacity
          onPress={() => setShowForm(true)}
          className="bg-purple-600 rounded-full p-2"
        >
          <Plus size={20} color="white" />
        </TouchableOpacity>
      </View>

      <ScrollView className="flex-1 px-6 py-6">
        {/* Sleep Summary */}
        <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-100">
          <Text className="text-xl font-semibold text-gray-900 mb-4">Sleep Overview</Text>
          
          <View className="flex-row justify-between items-center mb-4">
            <View className="items-center flex-1">
              <Text className="text-3xl font-bold text-purple-600">
                {averageSleepDuration?.toFixed(1) || '0'}h
              </Text>
              <Text className="text-gray-600 text-sm">Avg Sleep</Text>
            </View>
            <View className="items-center flex-1">
              <Text className="text-3xl font-bold text-blue-600">7</Text>
              <Text className="text-gray-600 text-sm">Days Tracked</Text>
            </View>
            <View className="items-center flex-1">
              <Text className="text-3xl font-bold text-green-600">85%</Text>
              <Text className="text-gray-600 text-sm">Sleep Score</Text>
            </View>
          </View>

          <View className="bg-purple-50 rounded-xl p-4">
            <Text className="text-purple-800 font-medium text-center">
              {averageSleepDuration && averageSleepDuration >= 7 
                ? "Great sleep habits! Keep it up." 
                : "Try to get 7-9 hours of sleep for optimal health."}
            </Text>
          </View>
        </View>

        {/* Sleep Tips */}
        <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-100">
          <Text className="text-xl font-semibold text-gray-900 mb-4">Sleep Tips</Text>
          
          <View className="space-y-3">
            <View className="flex-row items-start">
              <View className="w-2 h-2 rounded-full bg-purple-600 mt-2 mr-3" />
              <Text className="text-gray-700 flex-1">
                Maintain a consistent sleep schedule, even on weekends
              </Text>
            </View>
            <View className="flex-row items-start">
              <View className="w-2 h-2 rounded-full bg-purple-600 mt-2 mr-3" />
              <Text className="text-gray-700 flex-1">
                Create a relaxing bedtime routine to signal your body it's time to sleep
              </Text>
            </View>
            <View className="flex-row items-start">
              <View className="w-2 h-2 rounded-full bg-purple-600 mt-2 mr-3" />
              <Text className="text-gray-700 flex-1">
                Avoid screens for at least 1 hour before bedtime
              </Text>
            </View>
            <View className="flex-row items-start">
              <View className="w-2 h-2 rounded-full bg-purple-600 mt-2 mr-3" />
              <Text className="text-gray-700 flex-1">
                Keep your bedroom cool, dark, and quiet
              </Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <Text className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</Text>
          
          <TouchableOpacity
            onPress={() => setShowForm(true)}
            className="bg-purple-600 rounded-xl p-4 flex-row items-center justify-center mb-3"
          >
            <Plus size={20} color="white" />
            <Text className="text-white font-semibold ml-2">Log Last Night's Sleep</Text>
          </TouchableOpacity>

          <TouchableOpacity className="bg-blue-50 rounded-xl p-4 flex-row items-center justify-center">
            <TrendingUp size={20} color="#2563eb" />
            <Text className="text-blue-600 font-semibold ml-2">View Sleep Trends</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}