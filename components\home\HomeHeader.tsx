import React from 'react';
import { View, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { theme } from '@/constants/theme';
import { useUser } from '@clerk/clerk-expo';
import { Text } from '@/components/ui/text';
import { router } from 'expo-router';

export function HomeHeader() {
  const { user } = useUser();

  const timeOfDay = new Date().getHours();

  const greeting =
    timeOfDay < 12
      ? 'Good morning'
      : timeOfDay < 18
        ? 'Good afternoon'
        : 'Good evening';

  return (
    <View style={styles.header}>
      <View>
        <Text style={styles.greeting}>{greeting},</Text>
        <Text style={styles.name}>{user?.firstName}</Text>
      </View>
      <TouchableOpacity
        style={styles.avatar}
        onPress={() => router.push('/profile')}
      >
        <Image
          source={{
            uri: user?.imageUrl,
          }}
          style={styles.avatar}
        />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  greeting: {
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.l,
    color: theme.colors.gray[600],
    marginBottom: 4,
  },
  name: {
    fontFamily: theme.typography.fontFamily.bold,
    fontSize: theme.typography.fontSize.xxl,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
});
