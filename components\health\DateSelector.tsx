import React, { useMemo } from 'react';
import { View, TouchableOpacity, ScrollView } from 'react-native';
import { ChevronLeft, ChevronRight, Calendar } from 'lucide-react-native';
import DateTimePicker, {
  DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Text } from '../ui/text';
import { cn } from '@/lib/utils';

type DateSelectorProps = {
  /** The currently selected date */
  selectedDate: Date;
  /** Callback when the selected date changes */
  onDateChange: (date: Date) => void;
};

export function DateSelector({
  selectedDate,
  onDateChange,
}: DateSelectorProps) {
  const [isCalendarVisible, setIsCalendarVisible] = React.useState(false);

  const dates = useMemo(() => {
    const dates = [];
    for (let i = -3; i <= 3; i++) {
      const date = new Date(selectedDate);
      date.setDate(date.getDate() + i);
      dates.push(date);
    }
    return dates;
  }, [selectedDate]);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      day: 'numeric',
    });
  };

  const formatFullDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isSelected = (date: Date) => {
    return date.toDateString() === selectedDate.toDateString();
  };

  const handleDateChange = (event: DateTimePickerEvent, date?: Date) => {
    // Only update if the user confirmed the selection
    if (event.type === 'set' && date) {
      onDateChange(new Date(date));
      setIsCalendarVisible(false);
    } else if (event.type === 'dismissed') {
      setIsCalendarVisible(false);
    }
  };

  return (
    <View className="pb-3">
      <View className="flex-row items-center px-3 py-4">
        <TouchableOpacity
          className="p-2"
          onPress={() => {
            const newDate = new Date(selectedDate);
            newDate.setDate(newDate.getDate() - 1);
            onDateChange(newDate);
          }}
        >
          <ChevronLeft size={24} color="#606060" />
        </TouchableOpacity>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: 'center',
            paddingHorizontal: 12,
          }}
        >
          {dates.map((date, index) => (
            <TouchableOpacity
              key={index}
              className={cn(
                'px-3 py-2 rounded-lg mx-1',
                isSelected(date) ? 'bg-primary' : 'bg-gray-100',
                isToday(date) && 'border border-primary',
              )}
              onPress={() => onDateChange(date)}
            >
              <Text
                className={cn(
                  'font-medium text-sm',
                  isSelected(date) ? 'text-white' : 'text-gray-800',
                  isToday(date) && !isSelected(date) && 'text-primary',
                )}
              >
                {formatDate(date)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <TouchableOpacity
          className="p-2"
          onPress={() => {
            const newDate = new Date(selectedDate);
            newDate.setDate(newDate.getDate() + 1);
            onDateChange(newDate);
          }}
        >
          <ChevronRight size={24} color="#606060" />
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        className="flex-row items-center justify-center py-2 mx-4 rounded-lg bg-gray-50"
        onPress={() => setIsCalendarVisible(true)}
      >
        <Calendar size={16} color="#9B6BDF" />
        <Text className="font-medium text-sm text-primary ml-2">
          {formatFullDate(selectedDate)}
        </Text>
      </TouchableOpacity>

      <Dialog open={isCalendarVisible} onOpenChange={setIsCalendarVisible}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Select Date</DialogTitle>
          </DialogHeader>
          <DateTimePicker
            value={selectedDate}
            mode="date"
            display="calendar"
            onChange={handleDateChange}
            style={{ width: '100%' }}
          />
        </DialogContent>
      </Dialog>
    </View>
  );
}
