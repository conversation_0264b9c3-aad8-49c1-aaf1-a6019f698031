import React, { useState, useEffect, useMemo } from 'react';
import { View, Alert, TouchableOpacity } from 'react-native';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Text } from '@/components/ui/text';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Spinner } from '@/components/ui/spinner';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { useRouter } from 'expo-router';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Header } from '@/components/ui/Header';
import { ScrollView } from 'react-native-gesture-handler';

const PREDEFINED_ACTIVITY_LEVELS = [
  { label: 'Sedentary', value: 'sedentary', icon: 'couch' },
  { label: 'Lightly Active', value: 'lightly_active', icon: 'walk' },
  { label: 'Moderately Active', value: 'moderately_active', icon: 'run' },
  { label: 'Very Active', value: 'very_active', icon: 'bike' },
  { label: 'Extra Active', value: 'extra_active', icon: 'dumbbell' },
];

export default function UpdateProfileScreen() {
  //

  const router = useRouter();
  const userProfile = useQuery(api.userProfile.getProfile);
  const createProfile = useMutation(api.userProfile.createProfile);
  const updateProfile = useMutation(api.userProfile.updateProfile);

  type GenderType = 'male' | 'female' | 'other';
  type ActivityLevelType =
    | 'sedentary'
    | 'lightly_active'
    | 'moderately_active'
    | 'very_active'
    | 'extra_active';

  const [dateOfBirth, setDateOfBirth] = useState('');
  const [weight, setWeight] = useState('');
  const [height, setHeight] = useState('');
  const [gender, setGender] = useState<GenderType | ''>('');
  const [activityLevel, setActivityLevel] = useState<ActivityLevelType | ''>(
    '',
  );
  const [targetWeight, setTargetWeight] = useState('');
  const [targetCalories, setTargetCalories] = useState('');
  const [loading, setLoading] = useState(false);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [formProgress, setFormProgress] = useState(0);

  useEffect(() => {
    if (userProfile) {
      setDateOfBirth(userProfile.dateOfBirth || '');
      setWeight(userProfile.weight?.toString() || '');
      setHeight(userProfile.height?.toString() || '');
      setGender((userProfile.gender as GenderType) || '');
      setActivityLevel((userProfile.activityLevel as ActivityLevelType) || '');
      setTargetWeight(userProfile.targetWeight?.toString() || '');
      setTargetCalories(userProfile.targetCalories?.toString() || '');
    }
  }, [userProfile]);

  useEffect(() => {
    const fields = [
      dateOfBirth,
      weight,
      height,
      gender,
      activityLevel,
      targetWeight,
      targetCalories,
    ];
    const filledFields = fields.filter(
      (field) =>
        field !== undefined &&
        field !== null &&
        (typeof field === 'string' ? field.trim() !== '' : true),
    ).length;
    setFormProgress((filledFields / fields.length) * 100);
  }, [
    dateOfBirth,
    weight,
    height,
    gender,
    activityLevel,
    targetWeight,
    targetCalories,
  ]);

  const bmi = useMemo(() => {
    if (weight && height) {
      const weightKg = parseFloat(weight);
      const heightM = parseFloat(height) / 100;
      if (weightKg > 0 && heightM > 0) {
        return (weightKg / (heightM * heightM)).toFixed(1);
      }
    }
    return null;
  }, [weight, height]);

  const estimatedCalories = useMemo(() => {
    if (weight && height && gender && activityLevel && dateOfBirth) {
      const weightKg = parseFloat(weight);
      const heightCm = parseFloat(height);
      const birthDate = new Date(dateOfBirth);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();

      if (isNaN(age) || age < 0 || age > 120) return null;

      let bmr = 0;
      if (gender === 'male') {
        bmr = 88.362 + 13.397 * weightKg + 4.799 * heightCm - 5.677 * age;
      } else if (gender === 'female') {
        bmr = 447.593 + 9.247 * weightKg + 3.098 * heightCm - 4.33 * age;
      }

      const activityMultipliers: Record<string, number> = {
        sedentary: 1.2,
        lightly_active: 1.375,
        moderately_active: 1.55,
        very_active: 1.725,
        extra_active: 1.9,
      };

      const multiplier = activityMultipliers[activityLevel] || 1.2;
      return Math.round(bmr * multiplier);
    }
    return null;
  }, [weight, height, gender, activityLevel, dateOfBirth]);

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setProfileImage(result.assets[0].uri);
    }
  };

  const handleSaveProfile = async () => {
    setLoading(true);
    try {
      const profileData = {
        dateOfBirth: dateOfBirth || undefined,
        weight: weight ? parseFloat(weight) : undefined,
        height: height ? parseFloat(height) : undefined,
        gender: gender as GenderType | undefined,
        activityLevel: activityLevel as ActivityLevelType | undefined,
        targetWeight: targetWeight ? parseFloat(targetWeight) : undefined,
        targetCalories: targetCalories ? parseFloat(targetCalories) : undefined,
        imageUrl: profileImage || undefined,
      };

      if (userProfile) {
        await updateProfile(profileData as any);
        Alert.alert('Success', 'Profile updated successfully!');
      } else {
        await createProfile(profileData as any);
        Alert.alert('Success', 'Profile created successfully!');
      }

      router.back();
    } catch (error) {
      console.error('Failed to save profile:', error);
      Alert.alert('Error', 'Failed to save profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    if (userProfile) {
      setDateOfBirth(userProfile.dateOfBirth || '');
      setWeight(userProfile.weight?.toString() || '');
      setHeight(userProfile.height?.toString() || '');
      setGender((userProfile.gender as GenderType) || '');
      setActivityLevel((userProfile.activityLevel as ActivityLevelType) || '');
      setTargetWeight(userProfile.targetWeight?.toString() || '');
      setTargetCalories(userProfile.targetCalories?.toString() || '');
    } else {
      setDateOfBirth('');
      setWeight('');
      setHeight('');
      setGender('');
      setActivityLevel('');
      setTargetWeight('');
      setTargetCalories('');
      setProfileImage(null);
    }
  };

  if (!userProfile) {
    return (
      <SafeAreaView className="flex-1 bg-background p-4 items-center justify-center">
        <Spinner size="large" />
        <Text className="mt-4">Loading profile...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background">
      <Header title="Update Profile" />
      <ScrollView
        contentContainerStyle={{
          padding: 24,
        }}
        showsVerticalScrollIndicator={false}
      >
        <View className="items-center mb-8">
          <TouchableOpacity onPress={pickImage} className="mb-4">
            <Avatar
              className="w-32 h-32 border-4 border-primary"
              alt="profile image"
            >
              <AvatarImage
                source={{
                  uri: profileImage || 'https://github.com/shadcn.png',
                }}
              />
              <AvatarFallback>
                <Text className="text-2xl">
                  {userProfile?.userId?.[0]?.toUpperCase() || 'U'}
                </Text>
              </AvatarFallback>
            </Avatar>
            <View className="absolute bottom-2 right-2 bg-primary rounded-full p-2">
              <Ionicons name="camera" size={20} color="white" />
            </View>
          </TouchableOpacity>
          <Progress value={formProgress} className="w-full h-2" />
          <Text className="text-sm text-muted-foreground mt-2">
            {Math.round(formProgress)}% complete
          </Text>
        </View>

        <View className="mb-6">
          <Text className="text-xl font-bold mb-4 text-foreground">
            Personal & Physical
          </Text>
          <View className="mb-4">
            <Label className="mb-2 text-muted-foreground">
              Date of Birth (YYYY-MM-DD)
            </Label>
            <Input
              placeholder="e.g., 1990-01-15"
              value={dateOfBirth}
              onChangeText={setDateOfBirth}
              keyboardType="numbers-and-punctuation"
            />
          </View>
          <View className="mb-4">
            <Label className="mb-2 text-muted-foreground">Gender</Label>
            <ToggleGroup
              type="single"
              value={gender || ''}
              onValueChange={(value) => setGender(value as GenderType)}
              className="flex-row justify-start"
            >
              <ToggleGroupItem value="male" aria-label="Male">
                <Text>Male</Text>
              </ToggleGroupItem>
              <ToggleGroupItem value="female" aria-label="Female">
                <Text>Female</Text>
              </ToggleGroupItem>
              <ToggleGroupItem value="other" aria-label="Other">
                <Text>Other</Text>
              </ToggleGroupItem>
            </ToggleGroup>
          </View>
          <View className="flex-row gap-4 mb-2">
            <View className="flex-1">
              <Label className="mb-2 text-muted-foreground">Weight (kg)</Label>
              <Input
                placeholder="e.g., 70"
                value={weight}
                onChangeText={setWeight}
                keyboardType="numeric"
              />
            </View>
            <View className="flex-1">
              <Label className="mb-2 text-muted-foreground">Height (cm)</Label>
              <Input
                placeholder="e.g., 175"
                value={height}
                onChangeText={setHeight}
                keyboardType="numeric"
              />
            </View>
          </View>
          {bmi && (
            <Text className="text-sm text-muted-foreground">BMI: {bmi}</Text>
          )}
        </View>

        <View className="mb-6">
          <Text className="text-xl font-bold mb-4 text-foreground">
            Activity & Goals
          </Text>
          <View className="mb-4">
            <Label className="mb-2 text-muted-foreground">Activity Level</Label>
            <ToggleGroup
              type="single"
              value={activityLevel || ''}
              onValueChange={(value) =>
                setActivityLevel(value as ActivityLevelType)
              }
              className="flex-wrap justify-start -m-1"
            >
              {PREDEFINED_ACTIVITY_LEVELS.map((item) => (
                <ToggleGroupItem
                  key={item.value}
                  value={item.value}
                  aria-label={item.label}
                  className="flex-row items-center gap-2 p-2 m-1"
                >
                  <MaterialCommunityIcons name={item.icon as any} size={20} />
                  <Text>{item.label}</Text>
                </ToggleGroupItem>
              ))}
            </ToggleGroup>
          </View>
          {estimatedCalories && (
            <Text className="text-sm text-muted-foreground mb-4">
              Estimated Daily Calories: {estimatedCalories} kcal
            </Text>
          )}
          <View className="flex-row gap-4">
            <View className="flex-1">
              <Label className="mb-2 text-muted-foreground">
                Target Weight (kg)
              </Label>
              <Input
                placeholder="e.g., 65"
                value={targetWeight}
                onChangeText={setTargetWeight}
                keyboardType="numeric"
              />
            </View>
            <View className="flex-1">
              <Label className="mb-2 text-muted-foreground">
                Target Calories
              </Label>
              <Input
                placeholder="e.g., 2000"
                value={targetCalories}
                onChangeText={setTargetCalories}
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>

        <Button
          onPress={handleSaveProfile}
          disabled={loading}
          className="rounded-full shadow-lg mt-4 h-12"
          size="lg"
        >
          {loading ? (
            <Spinner />
          ) : (
            <Text className="text-lg font-bold text-primary-foreground">
              Save Changes
            </Text>
          )}
        </Button>
      </ScrollView>
    </SafeAreaView>
  );
}
