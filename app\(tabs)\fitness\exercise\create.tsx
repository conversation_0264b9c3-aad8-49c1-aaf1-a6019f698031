import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { theme } from '@/constants/theme';
import { Plus, Minus, Save } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import DateTimePicker from '@react-native-community/datetimepicker';

type Exercise = {
  name: string;
  sets: number;
  reps: number;
  weight?: number;
  duration?: number;
  restTime?: number;
};

export default function CreateExercisePlanScreen() {
  const router = useRouter();
  const createPlan = useMutation(api.exercisePlans.createExercisePlan);

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));
  const [targetDaysPerWeek, setTargetDaysPerWeek] = useState('3');
  const [exercises, setExercises] = useState<Exercise[]>([
    { name: '', sets: 3, reps: 12 }
  ]);

  const handleAddExercise = () => {
    setExercises([...exercises, { name: '', sets: 3, reps: 12 }]);
  };

  const handleRemoveExercise = (index: number) => {
    setExercises(exercises.filter((_, i) => i !== index));
  };

  const handleExerciseChange = (index: number, field: keyof Exercise, value: any) => {
    const updatedExercises = [...exercises];
    updatedExercises[index] = {
      ...updatedExercises[index],
      [field]: value
    };
    setExercises(updatedExercises);
  };

  const handleCreate = async () => {
    try {
      await createPlan({
        name,
        description,
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        targetDaysPerWeek: parseInt(targetDaysPerWeek),
        exercises: exercises.filter(e => e.name.trim() !== ''),
      });
      router.back();
    } catch (error) {
      console.error('Error creating exercise plan:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.section}>
          <Text style={styles.label}>Plan Name</Text>
          <TextInput
            style={styles.input}
            value={name}
            onChangeText={setName}
            placeholder="Enter plan name"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={description}
            onChangeText={setDescription}
            placeholder="Enter plan description"
            multiline
            numberOfLines={4}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Date Range</Text>
          <View style={styles.dateContainer}>
            <View style={styles.dateInput}>
              <Text style={styles.dateLabel}>Start Date</Text>
              <DateTimePicker
                value={startDate}
                mode="date"
                display="default"
                onChange={(event, date) => date && setStartDate(date)}
              />
            </View>
            <View style={styles.dateInput}>
              <Text style={styles.dateLabel}>End Date</Text>
              <DateTimePicker
                value={endDate}
                mode="date"
                display="default"
                onChange={(event, date) => date && setEndDate(date)}
                minimumDate={startDate}
              />
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Target Days per Week</Text>
          <TextInput
            style={[styles.input, styles.numberInput]}
            value={targetDaysPerWeek}
            onChangeText={setTargetDaysPerWeek}
            keyboardType="number-pad"
            maxLength={1}
          />
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.label}>Exercises</Text>
            <TouchableOpacity 
              style={styles.addButton}
              onPress={handleAddExercise}
            >
              <Plus size={20} color={theme.colors.primary} />
              <Text style={styles.addButtonText}>Add Exercise</Text>
            </TouchableOpacity>
          </View>

          {exercises.map((exercise, index) => (
            <View key={index} style={styles.exerciseCard}>
              <View style={styles.exerciseHeader}>
                <Text style={styles.exerciseNumber}>Exercise {index + 1}</Text>
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => handleRemoveExercise(index)}
                >
                  <Minus size={20} color={theme.colors.error} />
                </TouchableOpacity>
              </View>

              <TextInput
                style={styles.input}
                value={exercise.name}
                onChangeText={(value) => handleExerciseChange(index, 'name', value)}
                placeholder="Exercise name"
              />

              <View style={styles.exerciseDetails}>
                <View style={styles.detailInput}>
                  <Text style={styles.detailLabel}>Sets</Text>
                  <TextInput
                    style={[styles.input, styles.numberInput]}
                    value={exercise.sets.toString()}
                    onChangeText={(value) => handleExerciseChange(index, 'sets', parseInt(value) || 0)}
                    keyboardType="number-pad"
                  />
                </View>

                <View style={styles.detailInput}>
                  <Text style={styles.detailLabel}>Reps</Text>
                  <TextInput
                    style={[styles.input, styles.numberInput]}
                    value={exercise.reps.toString()}
                    onChangeText={(value) => handleExerciseChange(index, 'reps', parseInt(value) || 0)}
                    keyboardType="number-pad"
                  />
                </View>

                <View style={styles.detailInput}>
                  <Text style={styles.detailLabel}>Weight (kg)</Text>
                  <TextInput
                    style={[styles.input, styles.numberInput]}
                    value={exercise.weight?.toString() || ''}
                    onChangeText={(value) => handleExerciseChange(index, 'weight', parseInt(value) || undefined)}
                    keyboardType="number-pad"
                    placeholder="Optional"
                  />
                </View>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity 
          style={[
            styles.createButton,
            (!name || exercises.every(e => !e.name.trim())) && styles.createButtonDisabled
          ]}
          onPress={handleCreate}
          disabled={!name || exercises.every(e => !e.name.trim())}
        >
          <Save size={20} color={theme.colors.white} />
          <Text style={styles.createButtonText}>Create Plan</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  content: {
    flex: 1,
    padding: theme.spacing.l,
  },
  section: {
    marginBottom: theme.spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.m,
  },
  label: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
    marginBottom: theme.spacing.s,
  },
  input: {
    borderWidth: 1,
    borderColor: theme.colors.gray[300],
    borderRadius: theme.borderRadius.m,
    padding: theme.spacing.m,
    fontFamily: theme.typography.fontFamily.regular,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  dateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: theme.spacing.m,
  },
  dateInput: {
    flex: 1,
  },
  dateLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    marginBottom: theme.spacing.xs,
  },
  numberInput: {
    textAlign: 'center',
    width: 80,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${theme.colors.primary}10`,
    paddingHorizontal: theme.spacing.m,
    paddingVertical: theme.spacing.s,
    borderRadius: theme.borderRadius.m,
  },
  addButtonText: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.primary,
    marginLeft: theme.spacing.xs,
  },
  exerciseCard: {
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.l,
    padding: theme.spacing.l,
    marginBottom: theme.spacing.m,
  },
  exerciseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.m,
  },
  exerciseNumber: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.gray[900],
  },
  removeButton: {
    padding: theme.spacing.xs,
  },
  exerciseDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.spacing.m,
  },
  detailInput: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
  detailLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: theme.typography.fontSize.s,
    color: theme.colors.gray[600],
    marginBottom: theme.spacing.xs,
  },
  footer: {
    padding: theme.spacing.l,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray[200],
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.m,
    borderRadius: theme.borderRadius.m,
  },
  createButtonDisabled: {
    backgroundColor: theme.colors.gray[300],
  },
  createButtonText: {
    fontFamily: theme.typography.fontFamily.semiBold,
    fontSize: theme.typography.fontSize.m,
    color: theme.colors.white,
    marginLeft: theme.spacing.s,
  },
});